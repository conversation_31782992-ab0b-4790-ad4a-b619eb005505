import React, { useEffect, useRef, useState, useMemo, useCallback } from 'react';
import { View, FlatList, TouchableOpacity, Animated, StyleSheet, Alert } from 'react-native';
import AntDesign from 'react-native-vector-icons/AntDesign';
import AdmobService from '../services/AdmobService';
import { BannerAdSize } from 'react-native-google-mobile-ads';
import {
  Text, useTheme,
} from 'react-native-paper';
import AIChatHeader from './AIChatHeader';
import ChatBox from './ChatBox';
import RenderMessage from './RenderItem';
import apiClient from '../services/ApiClient';
import { useRoute } from '@react-navigation/native';
import { useAIChat } from '../store/AIChatContext';
import { useQnAContext } from '../store/QnAContext';
import { useAICredit } from '../store/AICreditContext';
import { useLogin } from '../store/LoginContext';
import LoginModal from '../components/LoginModal';
import AICreditModal from '../components/AICreditModal';
import { useFocusEffect } from '@react-navigation/native';
import { usePurchase } from '../store/PurchaseContext';
import { useExamContext } from '../store/ExamContext';
import StickyBottomAdMob from '../components/StickyBottomAdMob';

// Add safety check for context
const useSafeAIChat = () => {
  const context = useAIChat();
  if (!context) {
    console.error('AIChatContext not found. Make sure AIChatProvider is wrapping your app.');
    return {
      getChatHistory: () => ({
        messages: [
          {
            id: 'welcome-message',
            choices: [{
              index: 0,
              message: {
                role: 'assistant',
                content: `I'm here to help with this Q&A question. What would you like to know?`,
                isLoading: false
              }
            }]
          }
        ],
        quickReplies: [
          'Explain why the answer is correct and others are wrong.',
          "Give an example to clarify the answer.",
          "Share references for the correct answer.",
        ]
      }),
      /* updateChatHistory: () => { } */
    };
  }
  return context;
};

const ChatScreen = () => {
  const theme = useTheme();
  const route = useRoute();
  const { examCode, qnaId } = route.params || {};
  const { getChatHistory, updateChatHistory } = useSafeAIChat();
  const { qnas } = useQnAContext();
  const { credits, useCredits } = useAICredit();
  const { isLoggedIn } = useLogin() || {};
  const { getSubscriptionsInfo } = usePurchase();
  const [subscriptionActive, setSubscriptionActive] = useState(false);
  const [subscriptionDetails, setSubscriptionDetails] = useState(null);
  const { selectedExam } = useExamContext();

  const refreshSubscription = useCallback(async () => {
    if (selectedExam?.exam_code && isLoggedIn) {
      console.log('[QnA] Refreshing subscription status using getSubscriptionsInfo');
      try {
        const subscriptionInfo = await getSubscriptionsInfo(selectedExam.exam_code, true);
        console.log('[QnA] Subscription info received:', subscriptionInfo);

        if (subscriptionInfo && subscriptionInfo.isActive) {
          setSubscriptionActive(true);
          setSubscriptionDetails(subscriptionInfo);
        } else {
          setSubscriptionActive(false);
          setSubscriptionDetails(null);
        }
      } catch (error) {
        console.error('Error checking subscription:', error);
        setSubscriptionActive(false);
        setSubscriptionDetails(null);
      }
    } else {
      setSubscriptionActive(false);
      setSubscriptionDetails(null);
    }
  }, [getSubscriptionsInfo, selectedExam]);

  
  // Refresh when screen comes into focus
  useFocusEffect(
    useCallback(() => {
      refreshSubscription();
    }, [refreshSubscription])
  );

  // State for login modal
  const [loginModalVisible, setLoginModalVisible] = useState(false);

  // Find the current question by qnaId
  const currentQuestion = qnas.find(q => q.id === qnaId);

  // Load saved chat history if available
  const initialChatState = useMemo(() => {
    const savedHistory = getChatHistory(examCode, qnaId);

    // If there's no saved history or it's empty, use default values
    if (!savedHistory || !savedHistory.messages || savedHistory.messages.length === 0) {
      return {
        messages: [
          {
            id: 'welcome-message',
            choices: [{
              index: 0,
              message: {
                role: 'assistant',
                content: `I'm here to help with this Q&A question. What would you like to know?`,
                isLoading: false
              }
            }]
          }
        ],
        quickReplies: [
          'Explain why the answer is correct and others are wrong.',
          "Give an example to clarify the answer.",
          "Share references for the correct answer.",
        ]
      };
    }

    return savedHistory;
  }, [examCode, qnaId, getChatHistory]);

  const [messages, setMessages] = useState(initialChatState.messages);
  const [inputMessage, setInputMessage] = useState('');
  const [quickReplies, setQuickReplies] = useState(initialChatState.quickReplies);
  const [adError, setAdError] = useState(null);
  const [loading, setLoading] = useState(false);
  const flatListRef = useRef(null);
  const fadeAnim = useRef(new Animated.Value(0)).current;
  const [creditModalVisible, setCreditModalVisible] = useState(false);
  const requestTimeoutRef = useRef(null);
  const abortControllerRef = useRef(null);
  const isInitialRender = useRef(true);

  // Function to handle message deletion
  const handleDeleteMessage = (messageId) => {
    // Find the message to delete
    const messageIndex = messages.findIndex(msg => msg.id === messageId);

    if (messageIndex === -1) return; // Message not found

    // Create a copy of the messages array
    const updatedMessages = [...messages];

    // Remove the message
    updatedMessages.splice(messageIndex, 1);

    // Update the messages state
    setMessages(updatedMessages);
  };

  useEffect(() => {
    Animated.timing(fadeAnim, {
      toValue: 1,
      duration: 800,
      useNativeDriver: true,
    }).start();
  }, [fadeAnim, quickReplies]);

  // Save chat history when component unmounts or messages/quickReplies change
  useEffect(() => {
    // Skip the initial render to prevent infinite loop
    if (isInitialRender.current) {
      isInitialRender.current = false;
      return;
    }

    if (examCode && qnaId) {
      // Use a debounce to prevent too frequent updates
      /* const timeoutId = setTimeout(() => {
        updateChatHistory(examCode, qnaId, messages, quickReplies);
      }, 300);

      return () => clearTimeout(timeoutId); */
    }
  }, [examCode, qnaId, messages, quickReplies/* , updateChatHistory */]);

  // Add a separate effect for unmount to ensure we save the final state
  /* useEffect(() => {
    return () => {
      if (examCode && qnaId) {
        updateChatHistory(examCode, qnaId, messages, quickReplies);
      }
    };
  }, []); */

  // Log route params for debugging
  useEffect(() => {
    /* console.log('ChatScreen opened with params:', { examCode, qnaId });
    console.log('Initial chat state:', {
      messageCount: initialChatState.messages.length,
      quickRepliesCount: initialChatState.quickReplies.length
    }); */
  }, [examCode, qnaId, initialChatState]);

  // Create refs for scroll behavior and measurements
  const scrollTimeoutRef = useRef(null);
  const lastScrollTimeRef = useRef(0);
  const flatListLayoutRef = useRef({ height: 0, width: 0 });
  const contentSizeRef = useRef({ height: 0, width: 0 });

  // TODO: Completely redesigned scrolling mechanism for reliable AI response positioning
  /* const scrollToLatestMessage = useCallback(() => {
    // Cancel any pending scroll
    if (scrollTimeoutRef.current) {
      clearTimeout(scrollTimeoutRef.current);
      scrollTimeoutRef.current = null;
    }

    // Use a timeout to ensure the UI has fully rendered the AI response
    // This is critical for accurate measurements
    scrollTimeoutRef.current = setTimeout(() => {
      if (flatListRef.current && messages.length > 0) {
        console.log('Executing new scrolling mechanism for AI response');

        // Find the last message in the chat
        const lastMessageIndex = messages.length - 1;
        const lastMessage = messages[lastMessageIndex];

        // Only apply special scrolling for AI assistant messages
        if (lastMessage && lastMessage.choices[0].message.role === 'assistant') {
          // Get current layout measurements
          const flatListHeight = flatListLayoutRef.current.height;
          const contentHeight = contentSizeRef.current.height;

          // Log measurements for debugging
          console.log('New scroll calculation:', {
            flatListHeight,
            contentHeight,
            messageCount: messages.length,
            lastMessageIndex
          });

          // Use scrollToIndex with viewOffset to position the message properly
          try {
            // First, measure the message content to determine if it's "short"
            const messageContent = lastMessage.choices[0].message.content || '';

            // A more accurate way to estimate message height based on content
            // Consider both length and number of paragraphs
            const paragraphCount = (messageContent.match(/\n\n/g) || []).length + 1;
            const lineCount = (messageContent.match(/\n/g) || []).length + 1;

            // Calculate approximate message height based on content characteristics
            // This is more accurate than the previous fixed multiplier
            const approximateHeight = Math.min(
              // Cap the maximum height estimation
              flatListHeight * 1.5,
              // Base height + height per paragraph + height per line
              80 + (paragraphCount * 40) + (lineCount * 22)
            );

            // Determine if the message is "short" relative to the visible area
            const isShortMessage = approximateHeight < (flatListHeight * 0.8);

            // Calculate remaining content below this message
            const remainingContentHeight = contentHeight - (
              // Estimate the position of this message in the content
              // This is more accurate as we use the actual index rather than a fixed multiplier
              (lastMessageIndex * (isShortMessage ? 150 : 200))
            );

            // Determine if the entire remaining content fits in the visible area
            const entireContentFitsScreen = remainingContentHeight < flatListHeight;

            console.log('New scroll decision factors:', {
              approximateHeight,
              isShortMessage,
              remainingContentHeight,
              entireContentFitsScreen,
              paragraphCount,
              lineCount
            });

            if (isShortMessage || entireContentFitsScreen) {
              // For short messages or when everything fits on screen, scroll to bottom
              // This provides a better user experience for short exchanges
              console.log('Message is short or fits on screen, scrolling to end');
              flatListRef.current.scrollToEnd({ animated: true });
            } else {
              // For longer messages, position the message at the top of the screen
              // This ensures users can see the complete response from the beginning
              console.log('Message is long, positioning at top of screen');

              flatListRef.current.scrollToIndex({
                index: lastMessageIndex,
                // viewPosition: 0 means the item will be at the top of the viewport
                viewPosition: 0,
                // Add a small offset to show the avatar and role label
                viewOffset: 40,
                animated: true
              });
            }
          } catch (error) {
            console.log('New scroll method primary approach failed:', error.message);

            // Simple fallback that's guaranteed to work
            // If our calculations fail, we'll just scroll to the message
            try {
              console.log('Using reliable fallback: scrollToIndex');
              flatListRef.current.scrollToIndex({
                index: lastMessageIndex,
                viewPosition: 0,
                animated: true
              });
            } catch (fallbackError) {
              // Last resort fallback
              console.log('All scroll methods failed, using scrollToEnd as final fallback');
              flatListRef.current.scrollToEnd({ animated: true });
            }
          }
        } else {
          // For user messages or other content, just scroll to end
          console.log('Not an AI response, using standard scroll to end');
          flatListRef.current.scrollToEnd({ animated: true });
        }

        // Update the last scroll time for tracking
        lastScrollTimeRef.current = Date.now();
      }
    }, 300); // Reduced delay for better responsiveness while still allowing render time
  }, [messages.length]); */

  // Enhanced content size change handler with improved measurement tracking
  const handleContentSizeChange = useCallback((contentWidth, contentHeight) => {
    // Store previous height for comparison
    const previousHeight = contentSizeRef.current.height;

    // Update content size ref for scroll calculations
    contentSizeRef.current = { width: contentWidth, height: contentHeight };

    // Log only significant changes to reduce console noise
    if (Math.abs(contentHeight - previousHeight) > 10) {
      console.log('Content size significantly changed:', {
        contentWidth,
        contentHeight,
        heightDifference: contentHeight - previousHeight
      });
    }

    // We intentionally don't trigger auto-scrolling here
    // This prevents unwanted scrolling when keyboard appears/disappears
    // or when content dimensions change for other reasons
    // Scrolling is only triggered after receiving an AI response
  }, []);

  // Optimized scroll handler that only tracks user-initiated scrolls
  const handleScroll = useCallback((event) => {/* 
    // We only need to know that the user has manually scrolled
    // This helps us avoid interrupting user scrolling with automatic scrolls

    // Check if this is a user-initiated scroll (not our programmatic scrolling)
    const currentTime = Date.now();
    const timeSinceLastAutoScroll = currentTime - lastScrollTimeRef.current;

    // If it's been more than 500ms since our last programmatic scroll,
    // we can assume this is a user-initiated scroll
    if (timeSinceLastAutoScroll > 500) {
      // We could track the user's scroll position here if needed in the future
      // For now, we just need to know that the user has manually scrolled
    } */
  }, []);

  // Track retry attempts to prevent infinite loops
  const retryCountRef = useRef(0);
  const MAX_RETRIES = 2; // Maximum number of automatic retries

  const handleUserInput = async (userText, isRetry = false) => {
    if (!userText.trim()) return;

    // Reset retry counter if this is a new request (not a retry)
    if (!isRetry) {
      retryCountRef.current = 0;
    }

    // Track when the request started
    const requestStartTime = Date.now();

    // Check if user is logged in
    if (!isLoggedIn) {
      setLoginModalVisible(true);
      return;
    }

    // Check if user has enough credits
    if (credits <= 0) {
      Alert.alert(
        'Out of Credits',
        'You have run out of AI credits. Please refill your credits to continue using the AI assistant.',
        [
          {
            text: 'Refill Credits',
            onPress: () => setCreditModalVisible(true)
          },
          {
            text: 'Cancel',
            style: 'cancel'
          }
        ]
      );
      return;
    }

    // Cancel any existing request
    if (abortControllerRef.current) {
      abortControllerRef.current.abort();
      clearTimeout(requestTimeoutRef.current);
    }

    // We'll deduct credits only after a successful API response
    // Create user message
    const userMessage = {
      id: Date.now().toString(),
      choices: [
        {
          index: 0,
          message: {
            role: 'user',
            content: userText,
          },
          logprobs: null,
          finish_reason: 'stop',
        },
      ],
    };

    const placeholderAssistantMessage = {
      id: `loading-${Date.now().toString()}`,
      choices: [
        {
          index: 0,
          message: {
            role: 'assistant',
            content: '...',
            isLoading: true,
          },
          logprobs: null,
          finish_reason: 'stop',
        },
      ],
    };

    setMessages(prev => [...prev, userMessage, placeholderAssistantMessage]);
    setLoading(true);

    // Scroll to bottom when user sends a message to show the loading indicator
    setTimeout(() => {
      if (flatListRef.current) {
        console.log('Scrolling to bottom after user sends message');
        flatListRef.current.scrollToEnd({ animated: true });
      }
    }, 100);

    // Create a new AbortController for this request
    abortControllerRef.current = new AbortController();

    // Set a timeout to automatically cancel the request if it takes too long
    requestTimeoutRef.current = setTimeout(() => {
      // Abort the request
      if (abortControllerRef.current) {
        abortControllerRef.current.abort();
        abortControllerRef.current = null;
      }

      // Remove both the placeholder message and the user message
      setMessages(prev =>
        prev.filter(msg => 
          msg.id !== placeholderAssistantMessage.id && 
          msg.id !== userMessage.id
        )
      );
      setLoading(false);

      // Notify the user with an alert dialog
      Alert.alert(
        'Request Timeout',
        'The AI is taking too long to respond. Please try again later.',
        [{ text: 'OK' }]
      );
    }, 8000); // 8-second timeout

    try {
      // Construct the payload for the AI API request
      let payload;

      // Get the specific chat history for this question from context
      const currentChatHistory = getChatHistory(examCode, qnaId);

      // Determine if this is the initial request by checking if there are only system messages
      // or if there's only the welcome message in the history
      const hasOnlyWelcomeMessage = currentChatHistory.messages.length === 1 &&
        currentChatHistory.messages[0].id === 'welcome-message';
      const hasNoUserMessages = !currentChatHistory.messages.some(msg =>
        msg.choices[0].message.role === 'user');

      const isInitialRequest = hasOnlyWelcomeMessage || hasNoUserMessages || messages.length <= 1;
      let formattedHistory = [];

      // Build question-specific context
      const questionContext = currentQuestion ? {
        question: currentQuestion?.content || '',
        choices: currentQuestion?.choices || [],
        correctAnswer: currentQuestion?.answer || ''
      } : null;

      if (isInitialRequest) {
        // Initial request - include full question context and user's query as the question
        payload = {
          question: userText, // Use the user's input as the question
          systemPrompt: questionContext,
          userQuery: userText,
          isInitialRequest: true, // Explicitly set to true for initial requests
          questionId: qnaId // Add question identifier
        };

        console.log('Sending initial request with user query:', userText);
      } else {
        // Follow-up request - use only the history for this specific question
        // Filter out loading messages, welcome message, and ensure we only include completed exchanges
        const filteredMessages = currentChatHistory.messages.filter(msg =>
          !msg.choices[0].message.isLoading &&
          msg.id !== 'welcome-message' &&
          msg.choices[0].message.content.trim() !== ''
        );

        // Format the chat history properly
        formattedHistory = filteredMessages.map(msg => ({
          role: msg.choices[0].message.role,
          content: msg.choices[0].message.content,
          timestamp: msg.id
        }));

        // Keep the current user message separate from history
        // This ensures the latest user message is properly identified as the current query
        payload = {
          question: userText, // Use the user's input as the question
          systemPrompt: questionContext,
          chatHistory: formattedHistory,
          userQuery: userText, // Current user message as the query
          isInitialRequest: false,
          questionId: qnaId // Add question identifier
        };
      }

      // Add detailed debug logging for the payload
      console.log('Sending API payload:', {
        question: payload.question,
        userQuery: payload.userQuery,
        isInitialRequest: payload.isInitialRequest,
        questionId: payload.questionId,
        // Omit large fields from console for readability
        systemPrompt: isInitialRequest ? '[system prompt included]' : undefined,
        chatHistory: !isInitialRequest ? `[${formattedHistory.length} messages]` : undefined,
        hasCurrentQuestion: currentQuestion !== null,
        messageCount: messages.length
      });

      const response = await apiClient.getAIResponse(payload, {
        signal: abortControllerRef.current.signal,
        skipTimeoutAlert: true, // We handle our own timeout alerts
      });

      // Clear the timeout since we got a response
      clearTimeout(requestTimeoutRef.current);

      console.log('apiClient.url:', apiClient.baseUrl);
      console.log('API Response:', JSON.stringify(response, null, 2));

      // Now that we have a successful response, deduct 1 credit
      const creditDeducted = await useCredits(1);
      if (creditDeducted) {
        console.log('Credit deducted after successful API response');
      } else {
        console.warn('Failed to deduct credit, but will still show response');
      }

      // Preserve the original formatting from the API
      const formattedAnswer = response.answer || '';

      const aiMessage = {
        id: Date.now().toString(),
        choices: [
          {
            index: 0,
            message: {
              role: 'assistant',
              content: formattedAnswer,
              isLoading: false,
              // Pass the raw answer as metadata
              metadata: {
                formattedAnswer: formattedAnswer,
                formattedExplanation: '' // We don't need separate explanation as it's included in the answer
              }
            },
            logprobs: null,
            finish_reason: 'stop',
          },
        ],
      };

      setMessages(prev =>
        prev.map(msg =>
          msg.id === placeholderAssistantMessage.id ? aiMessage : msg,
        ),
      );

      setQuickReplies(response.follow_up_questions || []);

      // Scroll to the bottom after receiving AI response
      // Using a simple scrollToEnd instead of the custom scrolling mechanism
      setTimeout(() => {
        if (flatListRef.current) {
          console.log('Scrolling to bottom after receiving AI response');
          flatListRef.current.scrollToEnd({ animated: true });
        }
      }, 300); // Short delay to ensure the UI has updated
    } catch (error) {
      // Clear the timeout since we got an error
      clearTimeout(requestTimeoutRef.current);

      // Only handle the error if it's not an abort error (which we triggered ourselves)
      if (error.name !== 'AbortError') {
        console.error('AI request failed:', error);

        // Determine the error message based on the error type
        let errorTitle = 'Error';
        let errorMessage = 'Sorry, there was an error processing your request. Please try again.';
        let canRetry = true;

        if (error.status === 401 || error.status === 403) {
          errorTitle = 'Authentication Required';
          errorMessage = 'Your session has expired. Please log in again to continue using the AI assistant.';
          canRetry = false; // Don't auto-retry auth errors
        } else if (error.message && (error.message.includes('network') || error.message.includes('Network'))) {
          errorTitle = 'Network Error';
          errorMessage = 'Please check your internet connection and try again.';
        } else if (error.message && error.message.includes('timeout')) {
          errorTitle = 'Request Timeout';
          errorMessage = 'The server took too long to respond. Please try again later.';
        } else if (error.status === 429) {
          errorTitle = 'Too Many Requests';
          errorMessage = 'You have made too many requests. Please wait a moment and try again.';
          canRetry = false; // Don't auto-retry rate limit errors
        } else if (error.status >= 500) {
          errorTitle = 'Server Error';
          errorMessage = 'Our servers are experiencing issues. Please try again later.';
          canRetry = retryCountRef.current < 1; // Only retry server errors once
        }

        // Remove both the loading placeholder and the last user message
        setMessages(prev =>
          prev.filter(msg => 
            msg.id !== placeholderAssistantMessage.id && 
            msg.id !== userMessage.id
          )
        );

        // Show appropriate alert based on error type
        if (error.status === 401 || error.status === 403) {
          // For authentication errors, show login prompt
          Alert.alert(
            errorTitle,
            errorMessage,
            [
              {
                text: 'Login',
                onPress: () => {
                  setLoginModalVisible(true);
                }
              },
              {
                text: 'Cancel',
                style: 'cancel'
              }
            ]
          );
        } else {
          // For other errors, show standard retry options
          Alert.alert(
            errorTitle,
            errorMessage,
            [
              {
                text: 'Try Again',
                onPress: () => {
                  // Reset retry counter for manual retry
                  retryCountRef.current = 0;
                  handleUserInput(userText, false);
                }
              },
              {
                text: 'Cancel',
                style: 'cancel'
              }
            ]
          );
        }
      }
    } finally {
      setLoading(false);
      abortControllerRef.current = null;
    }
  };

  const styles = StyleSheet.create({
    container: {
      flex: 1,
      backgroundColor: theme.colors.background,
    },
    quickReplyContainer: {
      flexDirection: 'column',
      justifyContent: 'space-around',
      marginVertical: 10,
      paddingHorizontal: 10,
    },
    quickReplyButton: {
      backgroundColor: theme.colors.background,
      padding: 10,
      borderRadius: 20,
      marginBottom: 10,
      borderColor: theme.colors.border,
      borderWidth: 1,
      flexDirection: 'row',
      alignItems: 'center',
    },
    quickReplyText: {
      color: theme.colors.assistanceText,
      width: '90%'
    },
    icon: {
      position: 'absolute',
      right: 12,
    },
  });

  // Save chat history when component unmounts or messages/quickReplies change
  useEffect(() => {
    return () => {
      if (examCode && qnaId) {
        updateChatHistory(examCode, qnaId, messages, quickReplies);
      }
    };
  }, [examCode, qnaId, messages, quickReplies]);

  return (
    <View style={styles.container}>
      <AIChatHeader title="Ask AI" />
      <FlatList
        ref={flatListRef}
        data={messages}
        keyExtractor={item => item.id}
        // Improve performance with optimized rendering
        removeClippedSubviews={false} // Keep all items mounted for better measurement accuracy
        maxToRenderPerBatch={10} // Render more items per batch for smoother scrolling
        windowSize={10} // Increase window size for better offscreen rendering
        // Maintain scroll position when content changes
        maintainVisibleContentPosition={{
          minIndexForVisible: 0,
          autoscrollToTopThreshold: 10 // Auto-scroll to top when items are added at the top
        }}
        renderItem={({ item }) => (
          <RenderMessage
            item={item}
            loading={loading}
            onDeleteMessage={handleDeleteMessage}
            onRetry={(errorItem) => {
              // Get the original user message that caused this error
              const userMessageIndex = messages.findIndex(msg =>
                msg.choices[0].message.role === 'user' &&
                messages.indexOf(msg) < messages.indexOf(errorItem)
              );

              if (userMessageIndex >= 0) {
                const userMessage = messages[userMessageIndex];
                const userText = userMessage.choices[0].message.content;

                // Remove the error message before retrying
                setMessages(prev =>
                  prev.filter(msg => msg.id !== errorItem.id)
                );

                // Reset retry counter for manual retry
                retryCountRef.current = 0;

                // Retry the request with the original user text
                handleUserInput(userText, false);
              }
            }}
          />
        )}
        onContentSizeChange={handleContentSizeChange}
        onLayout={(event) => {
          // Capture FlatList dimensions for scroll calculations
          const { width, height } = event.nativeEvent.layout;
          flatListLayoutRef.current = { width, height };

          console.log('FlatList layout updated:', { width, height });

          // Mark initial render as complete, no auto-scrolling
          if (isInitialRender.current) {
            isInitialRender.current = false;
          }
        }}
        /* // Optimized scroll event handling
        onScroll={handleScroll}
        onScrollBeginDrag={handleScroll}
        onScrollEndDrag={handleScroll}
        onMomentumScrollEnd={handleScroll}
        // Improve scroll behavior
        scrollEventThrottle={16} // 60fps for smooth scrolling */
        getItemLayout={(_, index) => ({
          length: 200, // Increased estimated height
          offset: 200 * index,
          index,
        })}
        ListFooterComponent={
          <>
            {!loading && quickReplies.length > 0 && (
              <Animated.View style={[styles.quickReplyContainer, { opacity: fadeAnim }]}>
                {quickReplies.map((reply, index) => (
                  <TouchableOpacity
                    key={index}
                    style={styles.quickReplyButton}
                    onPress={() => handleUserInput(reply)}
                  >
                    <Text style={styles.quickReplyText}>{reply}</Text>
                    <AntDesign
                      style={styles.icon}
                      name="arrowright"
                      color={theme.colors.onBackground}
                      size={24}
                    />
                  </TouchableOpacity>
                ))}
              </Animated.View>
            )}
          </>
        }
      />

      <StickyBottomAdMob subscriptionActive={subscriptionActive} />
      <ChatBox
        inputMessage={inputMessage}
        setInputMessage={setInputMessage}
        onHandleSend={() => {
          handleUserInput(inputMessage);
          setInputMessage('');
        }}
        disabled={credits <= 0 || !isLoggedIn}
      />
      {/* AI Credit Modal */}
      <AICreditModal
        visible={creditModalVisible}
        onDismiss={() => setCreditModalVisible(false)}
      />

      {/* Login Required Modal */}
      <LoginModal
        visible={loginModalVisible}
        onDismiss={() => setLoginModalVisible(false)}
        onLoginSuccess={() => {
          setLoginModalVisible(false);
        }}
        source="aiChat"
      />
    </View>

  );
};

export default ChatScreen;