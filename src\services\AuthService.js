import { GoogleSignin } from '@react-native-google-signin/google-signin';
import AsyncStorage from '@react-native-async-storage/async-storage';
import apiClient from './ApiClient';

import ContextUpdateService from './ContextUpdateService';

class AuthService {
  static async configureGoogleSignIn() {
    try {
      await GoogleSignin.configure({
        webClientId: '210517142673-9b5iess1sqfj2302cbkbo1i3fqa51en9.apps.googleusercontent.com',
        offlineAccess: true,
        scopes: ['profile', 'email'],
        forceCodeForRefreshToken: true,
      });
      console.log('[AuthService] Google Sign-In configured successfully');
    } catch (error) {
      console.error('[AuthService] Error configuring Google Sign-In:', error);
      throw error;
    }
  }

  static async loginWithGoogle() {
    try {
      await this.configureGoogleSignIn();

      // Check for Google Play Services
      try {
        await GoogleSignin.hasPlayServices({ showPlayServicesUpdateDialog: true });
        console.log('[AuthService] Google Play Services available');
      } catch (playServicesError) {
        console.error('[AuthService] Google Play Services error:', playServicesError);
        throw new Error('Google Play Services is not available or needs to be updated');
      }

      // Sign in with Google
      const userInfo = await GoogleSignin.signIn();

      console.log('[AuthService] Google Sign-In response structure:', {
        hasIdToken: !!userInfo.idToken,
        hasData: !!userInfo.data,
        hasDataIdToken: !!(userInfo.data && userInfo.data.idToken),
        hasServerAuthCode: !!userInfo.serverAuthCode,
        type: userInfo.type,
        keys: Object.keys(userInfo)
      });

      // Check if user cancelled the sign-in
      if (userInfo.type === 'cancelled' || userInfo.type === 'cancel') {
        console.log('[AuthService] User cancelled the Google Sign-In');
        throw new Error('SIGN_IN_CANCELLED');
      }

      // Try multiple ways to get the ID token
      let idToken = userInfo.idToken || (userInfo.data && userInfo.data.idToken);

      // If no token found and we have serverAuthCode, try to get tokens
      if (!idToken && userInfo.serverAuthCode) {
        try {
          console.log('[AuthService] Using serverAuthCode to get tokens');
          const tokens = await GoogleSignin.getTokens();
          console.log('[AuthService] Tokens response:', {
            hasIdToken: !!tokens.idToken,
            hasAccessToken: !!tokens.accessToken,
            keys: Object.keys(tokens)
          });
          idToken = tokens.idToken;
        } catch (tokenError) {
          console.error('[AuthService] Error getting tokens from serverAuthCode:', tokenError);
        }
      }

      // If still no token, try to sign out and sign in again (but only if user didn't cancel)
      if (!idToken && userInfo.type !== 'cancelled' && userInfo.type !== 'cancel') {
        try {
          console.log('[AuthService] Attempting to refresh token by signing out and in again...');
          await GoogleSignin.signOut();
          const freshUserInfo = await GoogleSignin.signIn();

          // Check if user cancelled the refresh attempt
          if (freshUserInfo.type === 'cancelled' || freshUserInfo.type === 'cancel') {
            console.log('[AuthService] User cancelled the token refresh attempt');
            throw new Error('SIGN_IN_CANCELLED');
          }

          idToken = freshUserInfo.idToken || (freshUserInfo.data && freshUserInfo.data.idToken);
        } catch (refreshError) {
          console.error('[AuthService] Error refreshing token:', refreshError);
          // If refresh failed due to cancellation, propagate the error
          if (refreshError.message === 'SIGN_IN_CANCELLED' || refreshError.code === 12501) {
            throw refreshError;
          }
        }
      }

      if (!idToken) {
        console.error('[AuthService] No ID token received after all attempts');
        console.error('[AuthService] UserInfo structure:', JSON.stringify(userInfo, null, 2));

        // Don't proceed with fallback authentication if user cancelled
        if (userInfo.type === 'cancelled' || userInfo.type === 'cancel') {
          console.log('[AuthService] Skipping fallback authentication due to user cancellation');
          throw new Error('SIGN_IN_CANCELLED');
        }

        // Last resort: check if we can use the user data without backend authentication
        console.log('[AuthService] Checking fallback user data:', {
          hasUserInfo: !!userInfo,
          hasUserInfoUser: !!(userInfo && userInfo.user),
          hasUserInfoUserId: !!(userInfo && userInfo.user && userInfo.user.id),
          userInfoUserKeys: (userInfo && userInfo.user) ? Object.keys(userInfo.user) : 'null'
        });

        if (userInfo && userInfo.user && userInfo.user.id) {
          console.warn('[AuthService] Proceeding without ID token - using user data directly');
          // Return user data without backend authentication
          const basicUserData = {
            id: userInfo.user.id,
            name: userInfo.user.name,
            email: userInfo.user.email,
            photo: userInfo.user.photo,
            token: null // No backend token available
          };

          console.log('[AuthService] Fallback basic user data created:', {
            hasId: !!basicUserData.id,
            hasName: !!basicUserData.name,
            hasEmail: !!basicUserData.email,
            hasPhoto: !!basicUserData.photo,
            userId: basicUserData.id
          });

          // Note: Cannot fetch complete user data without JWT token
          console.log('[AuthService] Cannot fetch complete user data without JWT token - using basic data only');
          const userData = basicUserData;

          console.log('[AuthService] Final fallback user data:', {
            userData: JSON.stringify(userData, null, 2),
            hasId: !!userData.id,
            hasName: !!userData.name,
            hasEmail: !!userData.email,
            hasPhoto: !!userData.photo,
            hasToken: !!userData.token,
            totalFields: Object.keys(userData).length,
            allFields: Object.keys(userData)
          });

          // Store user data in AsyncStorage
          await AsyncStorage.setItem('user', JSON.stringify(userData));



          return {
            success: true,
            user: userData,
            userInfo: userInfo,
            token: null,
            warning: 'Logged in without backend authentication'
          };
        }

        throw new Error('No ID token received from Google Sign-In');
      }

      console.log('[AuthService] Successfully obtained ID token, length:', idToken.length);

      // Store the Google ID token as bearer token for API calls
      console.log('[AuthService] Setting Google ID token as bearer token for API calls');
      apiClient.setAuthToken(idToken);

      // Store the Google ID token in AsyncStorage for persistence
      await AsyncStorage.setItem('google_id_token', idToken);
      console.log('[AuthService] Google ID token stored as bearer token and in AsyncStorage, length:', idToken.length);

      // Call backend API to authenticate using the Google ID token as bearer
      console.log('[AuthService] Calling loginWithGoogle with Google ID token as bearer token');
      const authResponse = await apiClient.loginWithGoogle(idToken);

      /* console.log('[AuthService] loginWithGoogle response received:', {
        response: authResponse,
        responseType: typeof authResponse,
        responseKeys: authResponse ? Object.keys(authResponse) : 'null',
        hasUser: !!(authResponse && (authResponse.user || authResponse.data)),
        userObject: authResponse && (authResponse.user || authResponse.data) ?
          JSON.stringify(authResponse.user || authResponse.data, null, 2) : 'No user object',
        fullResponse: JSON.stringify(authResponse, null, 2)
      }); */

      // Log the complete user object returned by loginWithGoogle
      if (authResponse && (authResponse.user || authResponse.data)) {
        const userObject = authResponse.user || authResponse.data;
        console.log('[AuthService] ===== COMPLETE USER OBJECT FROM loginWithGoogle =====');
        console.log('[AuthService] User Object:', JSON.stringify(userObject, null, 2));
        console.log('[AuthService] User Object Keys:', Object.keys(userObject));
        console.log('[AuthService] User Object Field Count:', Object.keys(userObject).length);
        console.log('[AuthService] ===== END USER OBJECT =====');
      } else {
        console.warn('[AuthService] No user object found in loginWithGoogle response');
      }

      console.log('[AuthService] Auth response received:', {
        hasResponse: !!authResponse,
        hasToken: !!(authResponse && authResponse.token),
        hasUser: !!(authResponse && authResponse.user),
        authResponseKeys: authResponse ? Object.keys(authResponse) : 'null',
        userInfoKeys: userInfo ? Object.keys(userInfo) : 'null',
        userInfoUserKeys: (userInfo && userInfo.user) ? Object.keys(userInfo.user) : 'null'
      });

      if (!authResponse || !authResponse.token) {
        throw new Error('Google login failed - no token in response');
      }

      // Debug user data extraction
      console.log('[AuthService] Extracting user data:', {
        authResponseUserId: authResponse.userId,
        authResponseUser: authResponse.user,
        authResponseUserKeys: authResponse.user ? Object.keys(authResponse.user) : 'null',
        userInfoUser: userInfo.user,
        userInfoUserKeys: userInfo.user ? Object.keys(userInfo.user) : 'null'
      });

      // Extract basic user data with proper fallbacks
      // Handle MongoDB _id format and regular id format
      const basicUserData = {
        id: authResponse.user?._id || authResponse.user?.id || authResponse.userId || userInfo.user?.id,
        googleId: authResponse.user?.googleId || userInfo.user?.id,
        name: authResponse.user?.displayName || userInfo.user?.name,
        email: authResponse.user?.email || userInfo.user?.email,
        photo: authResponse.user?.profilePicture || userInfo.user?.photo,
        token: authResponse.token
      };

      console.log('[AuthService] Basic user data extracted:', {
        hasId: !!basicUserData.id,
        hasName: !!basicUserData.name,
        hasEmail: !!basicUserData.email,
        hasPhoto: !!basicUserData.photo,
        hasToken: !!basicUserData.token,
        userId: basicUserData.id
      });

      // Use the complete user data directly from loginWithGoogle response
      console.log('[AuthService] Processing complete user data from loginWithGoogle response');
      const completeUserData = await this.mapUserDataFromResponse(authResponse.user, basicUserData.token);

      // Use the complete user data
      const userData = completeUserData;

      /* console.log('[AuthService] Final user data to be stored:', {
        userData: JSON.stringify(userData, null, 2),
        hasId: !!userData.id,
        hasName: !!userData.name,
        hasEmail: !!userData.email,
        hasPhoto: !!userData.photo,
        hasToken: !!userData.token,
        totalFields: Object.keys(userData).length,
        allFields: Object.keys(userData)
      }); */

      // Store user data in AsyncStorage
      await AsyncStorage.setItem('user', JSON.stringify(userData));

      // Update all React contexts with the user data
      console.log('[AuthService] Updating React contexts with user data');
      const contextUpdateSuccess = await ContextUpdateService.updateAllContextsAfterLogin(userData);
      if (contextUpdateSuccess) {
        console.log('[AuthService] All React contexts updated successfully');
      } else {
        console.warn('[AuthService] Some React contexts failed to update');
      }



      return {
        success: true,
        user: userData,
        userInfo: userInfo,
        token: authResponse.token,
        contextUpdateSuccess: contextUpdateSuccess
      };

    } catch (error) {
      console.error('[AuthService] Google login error:', error);

      // Provide more specific error information
      if (error && (error.code === 12501 || error.message === 'SIGN_IN_CANCELLED')) {
        console.log('[AuthService] User cancelled the sign-in process');
        throw new Error('SIGN_IN_CANCELLED');
      } else if (error && error.code === 7) {
        console.log('[AuthService] Network error during sign-in');
        throw new Error('Network error. Please check your internet connection.');
      } else if (error && error.message && error.message.includes('DEVELOPER_ERROR')) {
        console.log('[AuthService] Developer configuration error');
        throw new Error('Google Sign-In configuration error. Please contact support.');
      }

      // Ensure we always throw an Error object
      if (error instanceof Error) {
        throw error;
      } else {
        throw new Error(error ? String(error) : 'Unknown authentication error');
      }
    }
  }

  /**
   * Comprehensive data mapping function to process complete user object
   * Maps user data to appropriate formats for different contexts
   */
  static async mapUserDataFromResponse(userObject, token) {
    try {
      console.log('[AuthService] ===== STARTING COMPREHENSIVE DATA MAPPING =====');
      console.log('[AuthService] Raw user object received:', JSON.stringify(userObject, null, 2));

      // Handle MongoDB ObjectId format
      const userId = userObject._id?.$oid || userObject._id || userObject.id;

      // Extract core user data
      const coreUserData = {
        id: userId,
        _id: userObject._id,
        googleId: userObject.googleId,
        email: userObject.email,
        name: userObject.displayName || userObject.name,
        displayName: userObject.displayName,
        photo: userObject.profilePicture || userObject.photo,
        profilePicture: userObject.profilePicture,
        createdAt: userObject.createdAt,
        updatedAt: userObject.updatedAt,
        token: token
      };

      // Extract progress data
      const progressData = {
        courses: userObject.progress?.courses || [],
        lastActivity: userObject.progress?.lastActivity || null,
        progressId: userObject.progress?._id
      };

      // Extract quiz results
      const quizResults = userObject.quizResults || [];

      // Extract purchases data
      const purchases = userObject.purchases || [];

      // Extract AI credits data
      const aiCredits = {
        totalCredits: userObject.aiCredits?.totalCredits || 0,
        usedCredits: userObject.aiCredits?.usedCredits || 0,
        availableCredits: (userObject.aiCredits?.totalCredits || 0) - (userObject.aiCredits?.usedCredits || 0),
        transactions: userObject.aiCredits?.transactions || [],
        creditsId: userObject.aiCredits?._id
      };

      // Extract AI chats data
      const aiChats = userObject.aiChats || [];

      // Extract login history
      const loginHistory = userObject.loginHistory || [];

      // Create comprehensive mapped data
      const mappedData = {
        // Core user data
        ...coreUserData,

        // Context-specific data
        progress: progressData,
        quizResults: quizResults,
        purchases: purchases,
        aiCredits: aiCredits,
        aiChats: aiChats,
        loginHistory: loginHistory,

        // Additional metadata
        lastLoginAt: loginHistory.length > 0 ? loginHistory[loginHistory.length - 1].timestamp : null,
        totalPurchases: purchases.length,
        totalQuizResults: quizResults.length,
        totalAIChats: aiChats.length
      };

      console.log('[AuthService] ===== MAPPED DATA SUMMARY =====');
      console.log('[AuthService] Core User Data:', {
        id: mappedData.id,
        googleId: mappedData.googleId,
        email: mappedData.email,
        name: mappedData.name,
        hasPhoto: !!mappedData.photo
      });
      console.log('[AuthService] Progress Data:', {
        coursesCount: progressData.courses.length,
        lastActivity: progressData.lastActivity,
        progressId: progressData.progressId
      });
      console.log('[AuthService] Purchases Data:', {
        purchasesCount: purchases.length,
        purchases: purchases
      });
      console.log('[AuthService] AI Credits Data:', {
        totalCredits: aiCredits.totalCredits,
        usedCredits: aiCredits.usedCredits,
        availableCredits: aiCredits.availableCredits,
        transactionsCount: aiCredits.transactions.length
      });
      console.log('[AuthService] Quiz Results Data:', {
        quizResultsCount: quizResults.length
      });
      console.log('[AuthService] AI Chats Data:', {
        aiChatsCount: aiChats.length
      });
      console.log('[AuthService] ===== END MAPPED DATA SUMMARY =====');

      // Store context-specific data in AsyncStorage for persistence
      await this.storeContextData(mappedData);

      return mappedData;
    } catch (error) {
      console.error('[AuthService] Error in data mapping:', error);
      // Return basic user data if mapping fails
      return {
        id: userObject._id || userObject.id,
        googleId: userObject.googleId,
        email: userObject.email,
        name: userObject.displayName || userObject.name,
        photo: userObject.profilePicture || userObject.photo,
        token: token,
        progress: { courses: [], lastActivity: null },
        quizResults: [],
        purchases: [],
        aiCredits: { totalCredits: 0, usedCredits: 0, availableCredits: 0, transactions: [] },
        aiChats: [],
        loginHistory: []
      };
    }
  }

  /**
   * Store context-specific data in AsyncStorage for persistence
   */
  static async storeContextData(mappedData) {
    try {
      console.log('[AuthService] Storing context data in AsyncStorage');

      // Store user progress data
      if (mappedData.progress) {
        await AsyncStorage.setItem('user_progress', JSON.stringify(mappedData.progress));
        console.log('[AuthService] User progress data stored');
      }

      // Store quiz results data
      if (mappedData.quizResults) {
        await AsyncStorage.setItem('quiz_results', JSON.stringify(mappedData.quizResults));
        console.log('[AuthService] Quiz results data stored');
      }

      // Store purchases data
      if (mappedData.purchases) {
        await AsyncStorage.setItem('user_purchases', JSON.stringify(mappedData.purchases));
        console.log('[AuthService] Purchases data stored');
      }

      // Store AI credits data
      if (mappedData.aiCredits) {
        await AsyncStorage.setItem('ai_credits', JSON.stringify(mappedData.aiCredits));
        console.log('[AuthService] AI credits data stored');
      }

      // Store AI chats data
      if (mappedData.aiChats) {
        await AsyncStorage.setItem('ai_chats', JSON.stringify(mappedData.aiChats));
        console.log('[AuthService] AI chats data stored');
      }

      // Store user profile data for LoginContext
      const profileData = {
        _id: mappedData._id,
        id: mappedData.id,
        googleId: mappedData.googleId,
        email: mappedData.email,
        displayName: mappedData.displayName,
        name: mappedData.name,
        profilePicture: mappedData.profilePicture,
        photo: mappedData.photo,
        createdAt: mappedData.createdAt,
        updatedAt: mappedData.updatedAt
      };
      await AsyncStorage.setItem('user_profile', JSON.stringify(profileData));
      console.log('[AuthService] User profile data stored');

      console.log('[AuthService] All context data stored successfully');
    } catch (error) {
      console.error('[AuthService] Error storing context data:', error);
    }
  }

  static async logout() {
    try {
      console.log('[AuthService] Starting logout process');

      // Clear user data from AsyncStorage
      await AsyncStorage.removeItem('user');
      console.log('[AuthService] User data cleared from AsyncStorage');

      // Clear Google ID token from AsyncStorage
      await AsyncStorage.removeItem('google_id_token');
      console.log('[AuthService] Google ID token cleared from AsyncStorage');

      // Clear all context-specific data from AsyncStorage
      await this.clearAllContextData();
      console.log('[AuthService] All context data cleared from AsyncStorage');

      // Clear all React contexts
      console.log('[AuthService] Clearing all React contexts');
      const contextClearSuccess = await ContextUpdateService.clearAllContexts();
      if (contextClearSuccess) {
        console.log('[AuthService] All React contexts cleared successfully');
      } else {
        console.warn('[AuthService] Some React contexts failed to clear');
      }

      // Clear the bearer token from ApiClient
      apiClient.clearAuthToken();
      console.log('[AuthService] Bearer token cleared from ApiClient');

      // Stop real-time sync service
      try {
        const RealTimeSyncService = require('./RealTimeSyncService').default;
        RealTimeSyncService.stop();
        console.log('[AuthService] Real-time sync service stopped');
      } catch (syncError) {
        console.warn('[AuthService] Error stopping real-time sync service:', syncError.message);
      }

      // Call backend logout if needed
      try {
        await apiClient.logout();
        console.log('[AuthService] Backend logout successful');
      } catch (logoutError) {
        console.warn('[AuthService] Backend logout failed (this is often expected):', logoutError.message);
      }

      // Sign out from Google
      try {
        await GoogleSignin.signOut();
        console.log('[AuthService] Google Sign-In logout successful');
      } catch (googleLogoutError) {
        console.warn('[AuthService] Google Sign-In logout failed:', googleLogoutError.message);
      }

      console.log('[AuthService] Logout process completed successfully');
      return { success: true };
    } catch (error) {
      console.error('[AuthService] Logout error:', error);
      throw error;
    }
  }

  /**
   * Clear all context-specific data from AsyncStorage
   */
  static async clearAllContextData() {
    try {
      console.log('[AuthService] Clearing all context data from AsyncStorage');

      const contextKeys = [
        'user_progress',
        'quiz_results',
        'user_purchases',
        'ai_credits',
        'ai_chats',
        'user_profile'
      ];

      // Clear all context data
      await Promise.all(contextKeys.map(key => AsyncStorage.removeItem(key)));

      console.log('[AuthService] All context data cleared:', contextKeys);
    } catch (error) {
      console.error('[AuthService] Error clearing context data:', error);
    }
  }

  static async getCurrentUser() {
    try {
      const userJson = await AsyncStorage.getItem('user');
      return userJson ? JSON.parse(userJson) : null;
    } catch (error) {
      console.error('[AuthService] Error getting current user:', error);
      return null;
    }
  }

  /**
   * Restore authentication state on app startup
   * This should be called when the app starts to restore the Google ID token
   */
  static async restoreAuthState() {
    try {
      console.log('[AuthService] Restoring authentication state');

      // Get the stored Google ID token
      const storedGoogleToken = await AsyncStorage.getItem('google_id_token');

      if (storedGoogleToken) {
        console.log('[AuthService] Found stored Google ID token, restoring as bearer token, length:', storedGoogleToken.length);
        apiClient.setAuthToken(storedGoogleToken);
        console.log('[AuthService] Google ID token restored as bearer token');
        return true;
      } else {
        console.log('[AuthService] No stored Google ID token found');
        return false;
      }
    } catch (error) {
      console.error('[AuthService] Error restoring auth state:', error);
      return false;
    }
  }

  /**
   * Get the current Google ID token
   */
  static async getGoogleIdToken() {
    try {
      return await AsyncStorage.getItem('google_id_token');
    } catch (error) {
      console.error('[AuthService] Error getting Google ID token:', error);
      return null;
    }
  }
}

export default AuthService;
