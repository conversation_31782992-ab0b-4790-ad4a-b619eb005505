
import React, { useCallback, useEffect, useState, useMemo, useRef } from 'react';
import { useSubscriptionRefresh } from './utils/subscriptionUtils';
import { View, ScrollView, StyleSheet, Image } from 'react-native';
import {
  Text,
  useTheme,
  Appbar,
  Button
} from 'react-native-paper';
import { useNavigation } from '@react-navigation/native';
import UpgradeButton from './components/UpgradeButton';
import BuyNowModal from './components/BuyNowModal';
import EmptyStateCard from './components/EmptyStateCard';
import { useExamContext } from './store/ExamContext';
import { useQnAContext } from './store/QnAContext';
import { useUserProgress } from './store/UserProgressContext';
import SubjectCard from './components/SubjectCard';
import { useFilters } from './store/FilterContext';
import { usePurchase } from './store/PurchaseContext';
import { useLogin } from './store/LoginContext';
import StickyBottomAdMob from './components/StickyBottomAdMob';

const Qna = () => {
  const { setAppliedFilters } = useFilters();
  const { colors } = useTheme();
  const navigation = useNavigation();
  const [isBuyNowModalVisible, setIsBuyNowModalVisible] = useState(false);

  const { selectedExam, isLoading: examLoading } = useExamContext();
  const { currentExam, questionsBySubject, loadQnA, isLoading: qnaLoading } = useQnAContext();
  const { getSubscriptionsInfo, storeSubscriptionStatus } = usePurchase();
  const [subscriptionActive, setSubscriptionActive] = useState(false);
  const [subscriptionDetails, setSubscriptionDetails] = useState(null);
  const { isLoggedIn } = useLogin() || {};

  const refreshSubscription = useSubscriptionRefresh({
    selectedExam,
    isLoggedIn,
    getSubscriptionsInfo,
    setSubscriptionActive,
    setSubscriptionDetails
  });

  // Preload QnA data when exam is selected
  // Use a ref to track the last loaded exam to prevent duplicate loads
  const lastLoadedExamRef = useRef({
    examCode: null,
    examId: null,
    timestamp: 0
  });

  useEffect(() => {
    async function fetchData() {
      if (selectedExam?.exam_code && selectedExam?.id) {
        const examCode = selectedExam.exam_code;
        const examId = selectedExam.id;

        // Check if we already have questions for this exam
        const hasExistingData = currentExam === examCode && Object.keys(questionsBySubject).length > 0 && Object.values(questionsBySubject).some(subjectArray => subjectArray.length > 0);

        // Check if we've loaded this exam recently (within 5 seconds)
        const now = Date.now();
        const recentlyLoaded = lastLoadedExamRef.current.examCode === examCode && lastLoadedExamRef.current.examId === examId && now - lastLoadedExamRef.current.timestamp < 5000;

        console.log('[QnA] Exam Code:', examCode, 'Has Existing Data:', hasExistingData, 'Recently Loaded:', recentlyLoaded);

        // Only load if we don't have data AND haven't loaded recently
        if (!hasExistingData && !recentlyLoaded) {
          // Update the last loaded exam ref
          lastLoadedExamRef.current = { examCode, examId, timestamp: now };

          // This ensures we load the correct content type
          // loadQnA(examCode, !subscriptionActive, examId);
          await storeSubscriptionStatus(examCode, subscriptionActive);
        }
      }
    }

    fetchData();
  }, [selectedExam, currentExam, questionsBySubject]);


  const { progress } = useUserProgress();
  // Memoized subject data calculation
  const subjects = useMemo(() => {
    return selectedExam?.subjects?.map((subject, index) => {
      const subjectCode = subject.code ? Number(subject.code) : index + 1;
      const total = questionsBySubject[subjectCode]?.length || 0;

      // Updated progress access pattern
      const examProgress = progress[selectedExam?.id] || {};
      const subjectProgress = examProgress[subjectCode] || {};
      const browsed = subjectProgress.browsed?.length || 0;

      /* console.log('progress:',progress)
      console.log('examProgress:',examProgress)
      console.log('subjectProgress:',subjectProgress) */

      return {
        code: subjectCode,
        name: subject.name || subject,
        progress: total > 0 ? browsed / total : 0,
        percentage: total > 0 ? `${Math.round((browsed / total) * 100)}%` : '0%',
        browsed,
        total
      };
    }) || [];
  }, [selectedExam, questionsBySubject, progress]);

  const handleSubjectPress = useCallback((subjectCode, subjectName) => {
    if (questionsBySubject[subjectCode]?.length) {
      navigation.navigate('QnAList', { subjectCode, subjectName });
    }
  }, [questionsBySubject, navigation]);

  /* if (examLoading || qnaLoading) {
    return (
      <View style={styles.centered}>
        <ActivityIndicator size="large" />
      </View>
    );
  } */

  return (
    <View style={{ flex: 1, backgroundColor: colors.background }}>
      <Appbar.Header elevated>
        {/* App icon on the left */}
        <View style={{ marginLeft: 10, marginRight: 4 }}>
          <Image
            source={require('../android/app/src/main/res/mipmap-mdpi/ic_launcher_round.png')}
            style={{ width: 28, height: 28 }}
          />
        </View>

        {/* Show exam_code instead of exam_name */}
        <Appbar.Content
          title={selectedExam?.exam_code || ""}
          titleStyle={{
            paddingHorizontal: 12,
          }}
        />

        {/* Browse Exams button on the right */}
        <Button
          mode="text"
          onPress={() => navigation.navigate('Welcome2')}
          labelStyle={{ color: colors.primary, fontWeight: '600', fontSize: 14 }}
          compact
          style={{ marginRight: 8 }}
        >
          Browse Exams
        </Button>
      </Appbar.Header>

      {!selectedExam ? (
        <EmptyStateCard
          onAction={() => navigation.navigate('Welcome2')}
          subtitle="Please select an exam to view study materials"
        />
      ) : (
        <ScrollView
          contentContainerStyle={styles.container}
          showsVerticalScrollIndicator={false}
        >
          <Text variant="titleMedium" style={[styles.sectionTitle, { color: colors.onSurface }]}>
            Study Progress
          </Text>

          {subjects.map((subject) => (
            <SubjectCard
              key={subject.code}
              subject={subject}
              metricTitle="Browsed"
              metricValue={subject.browsed}
              onPress={() => handleSubjectPress(subject.code, subject.name)}
            />
          ))}

          <View style={styles.buttonGroup}>
            <Button
              mode="contained"
              icon="book"
              style={styles.reviewButton}
              labelStyle={{ color: '#FFFFFF', fontWeight: '600', fontSize: 16 }}
              theme={{ colors: { primary: colors.primary } }}
              onPress={() => {
                setAppliedFilters({ subjects: []/* , dateRange: null */ });
                navigation.navigate('QnAList', { reviewAll: true });
              }}
            >
              Review All Questions
            </Button>

            {!subscriptionActive && (
              <UpgradeButton onPress={() => setIsBuyNowModalVisible(true)} />
            )}
            <BuyNowModal
              buyNowModalVisible={isBuyNowModalVisible}
              setBuyNowModalVisible={setIsBuyNowModalVisible}
              selectedProduct={selectedExam}
              onPurchaseComplete={async (result) => {
                console.log('[QnA] Purchase completed, refreshing subscription status');
                await refreshSubscription();
              }}
            />
          </View>

          {/* {!subscriptionActive && (
            AdmobService.renderBannerAdContainer(
              BannerAdSize.BANNER,
              (error) => console.log('Banner ad failed to load:', error)
            )
          )} */}
        </ScrollView>
      )}
      <StickyBottomAdMob subscriptionActive={subscriptionActive} />
    </View>
  );
};


const styles = StyleSheet.create({
  container: {
    flexGrow: 1,
    padding: 16,
  },

  emptyState: {
    margin: 24,
    borderRadius: 16,
    elevation: 2,
  },
  emptyContent: {
    alignItems: 'center',
    paddingVertical: 32,
    paddingHorizontal: 24,
  },
  emptyIcon: {
    marginBottom: 16,
  },
  emptyText: {
    fontWeight: '600',
    marginBottom: 4,
    textAlign: 'center',
  },
  emptySubtext: {
    textAlign: 'center',
    marginBottom: 24,
    opacity: 0.8,
  },
  emptyButton: {
    borderRadius: 8,
    paddingHorizontal: 32,
  },
  sectionTitle: {
    fontWeight: '600',
    marginBottom: 5,
    marginLeft: 8,
  },
  cardPressable: {
    marginBottom: 12,
  },
  subjectCard: {
    borderRadius: 12,
    padding: 8,
    position: 'relative',
    marginBottom: 16,
  },
  cardHeader: {
    flexDirection: 'row',
    alignItems: 'center',
    marginBottom: 12,
  },
  bookIcon: {
    marginRight: 12,
  },
  subjectTitle: {
    flex: 1,
    fontWeight: '600',
    lineHeight: 22,
  },
  progressContainer: {
    marginLeft: 32,
  },
  statsRow: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    alignItems: 'center',
    marginBottom: 8,
  },
  progressText: {
    fontWeight: '700',
    fontSize: 15,
  },
  metaText: {
    fontSize: 13,
  },
  progressBar: {
    height: 6,
    borderRadius: 3,
  },
  chevron: {
    position: 'absolute',
    right: 16,
    top: '50%',
    marginTop: -12,
  },
  reviewButton: {
    marginBottom: 6,
    borderRadius: 8,
    paddingVertical: 6,
    width: '100%',
  },
  buttonGroup: {
    marginVertical: 6,
  },
  centered: {
    flex: 1,
    justifyContent: 'center',
    alignItems: 'center',
  },
});

export default Qna;