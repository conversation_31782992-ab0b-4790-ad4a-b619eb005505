import { useCallback } from 'react';
import { useFocusEffect } from '@react-navigation/native';

export const useSubscriptionRefresh = ({
  selectedExam,
  isLoggedIn,
  getSubscriptionsInfo,
  setSubscriptionActive,
  setSubscriptionDetails
}) => {
  const refreshSubscription = useCallback(async () => {
    if (selectedExam?.exam_code && isLoggedIn) {
      console.log('[useSubscriptionRefresh] Refreshing subscription status');
      try {
        const subscriptionInfo = await getSubscriptionsInfo(selectedExam.exam_code, true);
        console.log('[useSubscriptionRefresh] Subscription info:', subscriptionInfo);

        if (subscriptionInfo?.isActive) {
          setSubscriptionActive(true);
          setSubscriptionDetails(subscriptionInfo);
        } else {
          setSubscriptionActive(false);
          setSubscriptionDetails(null);
        }
      } catch (error) {
        console.error('Error checking subscription:', error);
        setSubscriptionActive(false);
        setSubscriptionDetails(null);
      }
    } else {
      setSubscriptionActive(false);
      setSubscriptionDetails(null);
    }
  }, [getSubscriptionsInfo, selectedExam, isLoggedIn]);

  // Auto-refresh when screen comes into focus
  useFocusEffect(
    useCallback(() => {
      refreshSubscription();
    }, [refreshSubscription])
  );

  return refreshSubscription;
};