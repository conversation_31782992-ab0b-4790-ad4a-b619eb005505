import { Platform, Alert } from 'react-native';
import AsyncStorage from '@react-native-async-storage/async-storage';
import { API_TOKEN, NODE_API_URL, APP_ENV } from '@env'; // Add APP_ENV import

// Debug logger function
const debug = (message, ...args) => {
    console.log(`[apiclient] ${message}`, ...args);
};

// Log environment variables for debugging
console.log('[ApiClient] Environment variables:', {
    NODE_API_URL: NODE_API_URL || 'not set',
    APP_ENV: APP_ENV || 'not set',
    API_TOKEN_LENGTH: API_TOKEN ? API_TOKEN.length : 0
});

class ApiClient {
    constructor() {
        this.baseUrl = NODE_API_URL;
        this.token = API_TOKEN;
        this.cache = new Map();
        this.cacheTTL = 30 * 60 * 1000; // 30 minutes
        this.cacheEnabled = APP_ENV !== 'dev';
        this.defaultTimeout = 10000; // 10 seconds
        this.retryCount = 1;
        this.retryDelay = 1000;
        this.cacheCleanupInterval = 5 * 60 * 1000; // Clean cache every 5 minutes

        // Log ApiClient configuration
        console.log('[ApiClient] Initialized with:', {
            baseUrl: this.baseUrl || 'not set',
            tokenLength: this.token ? this.token.length : 0,
            cacheEnabled: this.cacheEnabled,
            environment: APP_ENV
        });
        this.cleanupIntervalId = null;

        // Try to load the auth token from AsyncStorage
        this.initializeAuthToken();

        // Custom headers for requests
        this.headers = {
            'Content-Type': 'application/json',
            'Accept': 'application/json'
        };

        // Map to track in-flight requests
        this.pendingRequests = new Map();
        // Map to track failed requests for independent retry
        this.failedRequests = new Map();
        // Enable request deduplication by default
        this.deduplicationEnabled = true;

        // Special tracking for QnA requests to prevent duplicates during login flow
        this.qnaRequestTracker = {
            requests: {},
            add: (examCode, is_free) => {
                const key = `${examCode}_${is_free}`;
                this.qnaRequestTracker.requests[key] = {
                    timestamp: Date.now(),
                    examCode,
                    is_free
                };
                console.log(`[QNATRACKER] TRACKER_ADD: Added QnA request to tracker: ${key}`);
                debug(`[QNATRACKER] Added QnA request to tracker: ${key}`);

                // Log the current state of the tracker
                console.log(`[QNATRACKER] TRACKER_STATE: Current tracker has ${Object.keys(this.qnaRequestTracker.requests).length} entries`);
                Object.keys(this.qnaRequestTracker.requests).forEach(k => {
                    const entry = this.qnaRequestTracker.requests[k];
                    console.log(`[QNATRACKER] TRACKER_ENTRY: ${k}, age: ${Date.now() - entry.timestamp}ms`);
                });
            },
            check: (examCode, is_free, maxAgeMs = 10000) => {
                const key = `${examCode}_${is_free}`;
                console.log(`[QNATRACKER] TRACKER_CHECK: Checking for key: ${key}`);

                // Log the current state of the tracker
                console.log(`[QNATRACKER] TRACKER_STATE: Current tracker has ${Object.keys(this.qnaRequestTracker.requests).length} entries`);
                Object.keys(this.qnaRequestTracker.requests).forEach(k => {
                    const entry = this.qnaRequestTracker.requests[k];
                    console.log(`[QNATRACKER] TRACKER_ENTRY: ${k}, age: ${Date.now() - entry.timestamp}ms`);
                });

                const cached = this.qnaRequestTracker.requests[key];
                if (cached) {
                    const age = Date.now() - cached.timestamp;
                    if (age < maxAgeMs) {
                        console.log(`[QNATRACKER] TRACKER_HIT: Found recent QnA request in tracker: ${key}, age: ${age}ms`);
                        debug(`[QNATRACKER] Found recent QnA request in tracker: ${key}, age: ${age}ms`);
                        return true;
                    } else {
                        console.log(`[QNATRACKER] TRACKER_EXPIRED: Entry found but expired: ${key}, age: ${age}ms`);
                    }
                } else {
                    console.log(`[QNATRACKER] TRACKER_MISS: No entry found for key: ${key}`);
                }
                return false;
            },
            clear: () => {
                this.qnaRequestTracker.requests = {};
                console.log(`[QNATRACKER] TRACKER_CLEAR: Cleared QnA request tracker`);
                debug(`[QNATRACKER] Cleared QnA request tracker`);
            }
        };

        // Start cache cleanup interval
        if (this.cacheEnabled) {
            this.startCacheCleanup();
        }

        debug('Initializing ApiClient with configuration:', {
            baseUrl: this.baseUrl,
            tokenLength: this.token ? this.token.length : 0,
            cacheEnabled: this.cacheEnabled,
            defaultTimeout: this.defaultTimeout,
            retryCount: this.retryCount,
            retryDelay: this.retryDelay,
            deduplicationEnabled: this.deduplicationEnabled,
            environment: APP_ENV,
            platform: Platform.OS
        });

        if (!this.baseUrl) {
            throw new Error('NODE_API_URL is not defined');
        }

        if (typeof this.baseUrl !== 'string') {
            throw new Error('NODE_API_URL must be a string');
        }

        if (Platform.OS === 'android') {
            this.baseUrl = this.baseUrl
                .replace('localhost', '********')
                .replace('127.0.0.1', '********');
            debug('Android platform detected, adjusted baseUrl:', this.baseUrl);
        }
    }

    /**
     * Set a custom header for all requests
     * @param {string} key - Header key
     * @param {string} value - Header value
     */
    setHeader(key, value) {
        this.headers[key] = value;
        debug(`Set header: ${key}`);
    }

    /**
     * Remove a custom header
     * @param {string} key - Header key to remove
     */
    removeHeader(key) {
        delete this.headers[key];
        debug(`Removed header: ${key}`);
    }

    /**
     * Get all current headers
     * @returns {Object} - Current headers
     */
    getHeaders() {
        return { ...this.headers };
    }

    /**
     * Initialize authentication token from AsyncStorage
     * This is called during construction
     */
    async initializeAuthToken() {
        try {
            const storedToken = await AsyncStorage.getItem('auth_token');
            if (storedToken && typeof storedToken === 'string' && storedToken.length > 10) {
                this.token = storedToken;
                console.log('[ApiClient] Loaded auth token from AsyncStorage, length:', storedToken.length);
                debug('Loaded auth token from AsyncStorage');
            } else {
                console.log('[ApiClient] No valid auth token found in AsyncStorage, using default API token');
                debug('Using default API token (no valid token in AsyncStorage)');
            }
        } catch (error) {
            console.error('[ApiClient] Error loading auth token from AsyncStorage:', error);
            debug('Error loading auth token from AsyncStorage');
        }
    }

    /**
     * Set authentication token for API requests
     * @param {string} token - JWT authentication token
     */
    setAuthToken(token) {
        // Check if token is valid
        if (token && typeof token === 'string' && token.length > 10) {
            this.token = token;
            console.log('[ApiClient] Auth token set successfully, length:', token.length);
            console.log('[ApiClient] token:', token);
            debug('Auth token set successfully');
        } else {
            // If token is invalid, use default API token
            this.token = API_TOKEN;
            console.log('[ApiClient] Invalid token provided, using default API token');
            debug('Using default API token due to invalid token');
        }
    }

    /**
     * Clear authentication token and reset to default API token
     */
    clearAuthToken() {
        console.log('[ApiClient] Clearing auth token, resetting to default API token');
        this.token = API_TOKEN;
        debug('Auth token cleared, using default API token');
    }

    /**
     * Check if current token is valid (not the default API token)
     * @returns {boolean} - True if we have a custom auth token
     */
    hasValidAuthToken() {
        const isValid = this.token && this.token !== API_TOKEN && this.token.length > 10;
        debug(`Auth token validation: ${isValid ? 'valid' : 'invalid'}`, {
            hasToken: !!this.token,
            isDefaultToken: this.token === API_TOKEN,
            tokenLength: this.token ? this.token.length : 0
        });
        return isValid;
    }

    /**
     * Get current authentication status
     * @returns {Object} - Authentication status details
     */
    getAuthStatus() {
        return {
            hasToken: !!this.token,
            isDefaultToken: this.token === API_TOKEN,
            tokenLength: this.token ? this.token.length : 0,
            isValid: this.hasValidAuthToken()
        };
    }

    /**
     * Generate a unique request key based on endpoint and options
     * @param {string} endpoint - API endpoint
     * @param {Object} options - Request options
     * @returns {string} - Unique request key
     */
    generateRequestKey(endpoint, options = {}) {
        // Create a stable string representation of options by sorting keys
        const stableStringify = (obj) => JSON.stringify(Object.keys(obj).sort().reduce((acc, key) => {
            // Skip signal and onTimeout as they're unique per request
            if (key !== 'signal' && key !== 'onTimeout') {
                acc[key] = obj[key];
            }
            return acc;
        }, {}));

        return `${endpoint}-${stableStringify(options)}`;
    }

    /**
     * Start the cache cleanup interval
     */
    startCacheCleanup() {
        // Clear any existing interval
        if (this.cleanupIntervalId) {
            clearInterval(this.cleanupIntervalId);
        }

        // Set up new interval
        this.cleanupIntervalId = setInterval(() => {
            this.cleanExpiredCache();
        }, this.cacheCleanupInterval);

        console.log('[ApiClient] Cache cleanup interval started');
    }

    /**
     * Stop the cache cleanup interval
     */
    stopCacheCleanup() {
        if (this.cleanupIntervalId) {
            clearInterval(this.cleanupIntervalId);
            this.cleanupIntervalId = null;
            console.log('[ApiClient] Cache cleanup interval stopped');
        }
    }

    /**
     * Clean expired cache entries
     */
    cleanExpiredCache() {
        const now = Date.now();
        let expiredCount = 0;

        Array.from(this.cache.entries()).forEach(([key, { timestamp }]) => {
            if (now - timestamp >= this.cacheTTL) {
                this.cache.delete(key);
                expiredCount++;
            }
        });

        if (expiredCount > 0) {
            console.log(`[ApiClient] Cleaned up ${expiredCount} expired cache entries`);
        }
    }

    /**
     * Show network error alert to user
     * @param {string} endpoint - The endpoint that failed
     * @param {Error} error - The error that occurred
     * @param {Object} options - Additional options
     * @param {boolean} options.wasAbortedDueToTimeout - Whether the request was aborted due to our timeout
     * @param {number} options.timeout - The timeout value that was used
     * @param {boolean} options.forceShow - Force showing the alert even if rate limited
     */
    showNetworkErrorAlert(endpoint, error, options = {}) {
        // Only show timeout alerts if it was actually our timeout that triggered it
        // or if we're forcing the alert to show
        const isTimeout = (options.wasAbortedDueToTimeout === true) ||
            (error.message && error.message.includes('timeout'));

        // For AbortError, only treat as timeout if we know it was our timeout that caused it
        const isAbortButNotTimeout = error.name === 'AbortError' && !options.wasAbortedDueToTimeout;

        // If it's an abort but not our timeout, and we're not forcing the alert, don't show it
        if (isAbortButNotTimeout && !options.forceShow) {
            debug(`Skipping alert for aborted request that wasn't due to timeout: ${endpoint}`);
            return;
        }

        const title = isTimeout ? 'Request Timeout' : 'Network Error';

        // Create a more informative message based on the error type
        let message;
        if (isTimeout) {
            // For timeout errors, only include endpoint information in development builds
            const isDevelopment = APP_ENV === 'dev' || APP_ENV === 'development' || __DEV__;

            if (isDevelopment) {
                // Development: Include endpoint for debugging
                const shortEndpoint = endpoint.split('?')[0]; // Remove query parameters for display
                message = `The request to ${shortEndpoint} took too long to complete. This could be due to server load or network issues. Please try again later.`;
            } else {
                // Production: Generic message without endpoint information
                message = 'The request took too long to complete. This could be due to server load or network issues. Please try again later.';
            }
        } else {
            message = 'There was a problem connecting to the server. Please check your internet connection and try again later.';
        }

        // Log the error details for debugging
        const isDevelopment = APP_ENV === 'dev' || APP_ENV === 'development' || __DEV__;
        debug(`Showing alert for ${isTimeout ? 'timeout' : 'network'} error on ${endpoint}`, {
            errorName: error.name,
            errorMessage: error.message,
            wasAbortedDueToTimeout: options.wasAbortedDueToTimeout,
            timeout: options.timeout,
            isDevelopment,
            messageIncludesEndpoint: isTimeout && isDevelopment
        });

        // Only show the alert if we're not already showing too many alerts
        // This prevents alert spam if multiple requests fail at once
        if (options.forceShow || !this._isShowingTooManyAlerts()) {
            Alert.alert(
                title,
                message,
                [{ text: 'OK', onPress: () => console.log('Network error alert closed') }]
            );
        } else {
            debug('Suppressing additional error alert to prevent alert spam');
        }
    }

    /**
     * Check if we're showing too many alerts
     * @returns {boolean} - True if we should suppress additional alerts
     * @private
     */
    _isShowingTooManyAlerts() {
        // Simple rate limiting for alerts
        // We'll use a static property to track when the last alert was shown
        if (!ApiClient._lastAlertTime) {
            ApiClient._lastAlertTime = 0;
            ApiClient._alertCount = 0;
        }

        const now = Date.now();
        const timeSinceLastAlert = now - ApiClient._lastAlertTime;

        // Reset counter if it's been more than 5 seconds since the last alert
        if (timeSinceLastAlert > 5000) {
            ApiClient._alertCount = 0;
        }

        // Update the last alert time and increment the counter
        ApiClient._lastAlertTime = now;
        ApiClient._alertCount++;

        // Suppress alerts if we've shown more than 2 in the last 5 seconds
        return ApiClient._alertCount > 2;
    }

    /**
     * Retry a specific failed request independently
     * @param {string} requestKey - The key of the failed request
     * @returns {Promise} - The result of the retried request
     */
    async retryFailedRequest(requestKey) {
        if (!this.failedRequests.has(requestKey)) {
            throw new Error(`No failed request found with key: ${requestKey}`);
        }

        const { endpoint, options } = this.failedRequests.get(requestKey);
        console.log(`[ApiClient] Retrying failed request: ${endpoint}`);

        // Remove from failed requests map
        this.failedRequests.delete(requestKey);

        // Make a fresh request
        return this.request(endpoint, options);
    }

    async request(endpoint, options = {}) {
        const requestId = Math.random().toString(36).substring(2, 8); // Generate a short unique ID for this request

        // Generate a unique key for this request for both caching and deduplication
        const requestKey = this.generateRequestKey(endpoint, options);

        // Add more detailed logging for auth-related endpoints
        if (endpoint.includes('/auth')) {
            console.log(`[ApiClient] [${requestId}] Auth request started: ${endpoint}`, {
                method: options.method || 'GET',
                hasBody: !!options.body,
                bodyLength: options.body ? options.body.length : 0,
                timeout: options.timeout || this.defaultTimeout,
                baseUrl: this.baseUrl,
                tokenLength: this.token ? this.token.length : 0
            });
        }

        debug(`[${requestId}] Request started: ${endpoint}`, {
            method: options.method || 'GET',
            hasBody: !!options.body,
            timeout: options.timeout || this.defaultTimeout,
            requestKey
        });

        // Check cache first if enabled
        if (this.cacheEnabled && this.cache.has(requestKey)) {
            const { data, timestamp } = this.cache.get(requestKey);
            const age = Date.now() - timestamp;
            if (age < this.cacheTTL) {
                debug(`[${requestId}] Cache hit for: ${endpoint} (age: ${Math.round(age / 1000)}s)`);
                return data;
            } else {
                // Remove expired cache entry
                this.cache.delete(requestKey);
                debug(`[${requestId}] Removed expired cache for: ${endpoint} (age: ${Math.round(age / 1000)}s)`);
            }
        } else if (this.cacheEnabled) {
            debug(`[${requestId}] Cache miss for: ${endpoint}`);
        }

        // Check for duplicate in-flight requests if deduplication is enabled
        if (this.deduplicationEnabled && this.pendingRequests.has(requestKey)) {
            debug(`[${requestId}] Deduplicating request: ${endpoint}`);
            debug(`[${requestId}] Current pending requests: ${this.pendingRequests.size}`);
            debug(`[${requestId}] Pending request keys:`, Array.from(this.pendingRequests.keys()));
            return this.pendingRequests.get(requestKey);
        }

        // Create a new AbortController for this request
        const controller = new AbortController();
        // Merge with existing signal if provided
        const signal = options.signal || controller.signal;

        // Standardize timeout handling
        const timeout = options.timeout || this.defaultTimeout;
        debug(`[${requestId}] Using timeout: ${timeout}ms for ${endpoint}`);

        // Flag to track if request was aborted due to timeout
        let wasAbortedDueToTimeout = false;

        // Set up timeout for this request
        const timeoutId = setTimeout(() => {
            wasAbortedDueToTimeout = true;
            controller.abort();
            debug(`[${requestId}] Request timed out after ${timeout}ms: ${endpoint}`);
        }, timeout);

        // Add a listener to detect if the signal is aborted by something else
        signal.addEventListener('abort', () => {
            if (!wasAbortedDueToTimeout) {
                debug(`[${requestId}] Request was aborted by external source (not our timeout): ${endpoint}`);
            }
        });

        // Check if token is valid before using it
        const authToken = this.token || API_TOKEN;
        const tokenStatus = authToken === API_TOKEN ? 'default API token' :
            (authToken && authToken.length > 10) ? 'valid custom token' : 'invalid token';

        console.log(`[ApiClient] [${requestId}] Using ${tokenStatus} for request to ${endpoint}`);

        const headers = {
            'Authorization': `Bearer ${authToken}`,
            'X-Platform': Platform.OS,
            'X-App-Version': process.env.APP_VERSION || '1.0.0',
            'X-Request-ID': requestId, // Add request ID to headers for server-side tracking
            ...this.headers, // Include our custom headers
            ...options.headers, // Allow overriding headers per request
        };

        debug(`[${requestId}] Request headers:`, {
            ...headers,
            Authorization: headers.Authorization ? `Bearer ${authToken.substring(0, 5)}...${authToken.substring(authToken.length - 5)}` : undefined
        });

        // Create the request promise
        const requestPromise = (async () => {
            let attempt = 0;
            while (attempt < this.retryCount) {
                try {
                    debug(`[${requestId}] Attempt ${attempt + 1}/${this.retryCount} for ${endpoint}`);

                    // Add more detailed logging for auth-related endpoints
                    if (endpoint.includes('/auth')) {
                        console.log(`[ApiClient] [${requestId}] Making auth request to: ${this.baseUrl}${endpoint}`, {
                            method: options.method || 'GET',
                            bodyPreview: options.body ? options.body.substring(0, 50) + '...' : 'none'
                        });
                    }

                    const startTime = Date.now();
                    const response = await fetch(`${this.baseUrl}${endpoint}`, {
                        ...options,
                        headers,
                        signal,
                    });
                    const requestTime = Date.now() - startTime;

                    // Add more detailed logging for auth-related endpoints
                    if (endpoint.includes('/auth')) {
                        console.log(`[ApiClient] [${requestId}] Auth response received in ${requestTime}ms for ${endpoint}`, {
                            status: response.status,
                            statusText: response.statusText,
                            ok: response.ok
                        });
                    }

                    debug(`[${requestId}] Response received in ${requestTime}ms for ${endpoint}`);

                    clearTimeout(timeoutId);

                    if (!response.ok) {
                        const errorBody = await response.text();
                        debug(`[${requestId}] HTTP error response: ${response.status} - ${response.statusText}`, {
                            endpoint,
                            errorBody: errorBody.substring(0, 200) + (errorBody.length > 200 ? '...' : '')
                        });

                        // For auth endpoints, log more details
                        if (endpoint.includes('/auth')) {
                            console.error(`[ApiClient] [${requestId}] Auth request failed:`, {
                                status: response.status,
                                statusText: response.statusText,
                                url: `${this.baseUrl}${endpoint}`,
                                errorBody: errorBody
                            });
                        }

                        // Check for authentication errors (401/403)
                        if (response.status === 401 || response.status === 403) {
                            console.error(`[ApiClient] [${requestId}] Authentication error detected:`, {
                                status: response.status,
                                endpoint: endpoint,
                                currentTokenLength: this.token ? this.token.length : 0,
                                isDefaultToken: this.token === API_TOKEN,
                                errorBody: errorBody
                            });

                            // For user-related endpoints, this might indicate token expiration
                            if (endpoint.includes('/user/')) {
                                console.error(`[ApiClient] [${requestId}] User endpoint auth failure - token may be expired`);
                            }
                        }

                        // Create error message without exposing endpoint in production
                        const isDevelopment = APP_ENV === 'dev' || APP_ENV === 'development' || __DEV__;
                        const errorMessage = isDevelopment
                            ? `API Error: ${response.status} - ${response.statusText}\nURL: ${endpoint}\n${errorBody}`
                            : `API Error: ${response.status} - ${response.statusText}\n${errorBody}`;

                        // Create a proper error object with status information
                        const error = new Error(errorMessage);
                        error.status = response.status;
                        error.statusText = response.statusText;
                        error.endpoint = endpoint;
                        error.errorBody = errorBody;
                        
                        console.log(`[ApiClient] [${requestId}] Created HTTP error object with status:`, {
                            status: error.status,
                            statusText: error.statusText,
                            message: error.message,
                            endpoint: error.endpoint
                        });
                        
                        throw error;
                    }

                    let data;
                    try {
                        data = await response.json();

                        // Check if the response is undefined or null
                        if (data === undefined || data === null) {
                            console.error(`[ApiClient] [${requestId}] Response data is ${data === undefined ? 'undefined' : 'null'}:`, {
                                endpoint: endpoint,
                                status: response.status,
                                statusText: response.statusText,
                                headers: Object.fromEntries(response.headers.entries())
                            });
                            throw new Error(`API returned ${data === undefined ? 'undefined' : 'null'} response`);
                        }
                    } catch (jsonError) {
                        console.error(`[ApiClient] [${requestId}] Failed to parse JSON response:`, {
                            error: jsonError.message,
                            endpoint: endpoint,
                            status: response.status,
                            statusText: response.statusText
                        });

                        // Try to get the raw response text for debugging
                        try {
                            const responseClone = response.clone();
                            const rawText = await responseClone.text();
                            console.error(`[ApiClient] [${requestId}] Raw response text:`, rawText.substring(0, 500));
                        } catch (textError) {
                            console.error(`[ApiClient] [${requestId}] Could not read raw response text:`, textError);
                        }

                        throw new Error(`Failed to parse JSON response: ${jsonError.message}`);
                    }

                    // Add more detailed logging for auth-related endpoints
                    if (endpoint.includes('/auth')) {
                        console.log(`[ApiClient] [${requestId}] Auth response parsed for ${endpoint}:`,
                            JSON.stringify(data, (key, value) => {
                                // Redact sensitive information
                                if (key === 'token' && typeof value === 'string') {
                                    return `${value.substring(0, 10)}...${value.substring(value.length - 5)}`;
                                }
                                return value;
                            }, 2)
                        );
                    }

                    debug(`[${requestId}] Response parsed successfully for ${endpoint}`, {
                        success: data.success,
                        hasData: !!data.data,
                        dataType: data.data ? typeof data.data : null,
                        isArray: data.data ? Array.isArray(data.data) : false,
                        dataLength: data.data && Array.isArray(data.data) ? data.data.length : null
                    });

                    if (!data.success) {
                        debug(`[${requestId}] API reported error: ${data.message}`);
                        
                        // Create a proper error object with status information
                        const error = new Error(`API Error: ${data.message}`);
                        error.status = data.code || response.status; // Use API error code if available, fallback to HTTP status
                        error.statusText = data.error || response.statusText;
                        error.endpoint = endpoint;
                        error.errorBody = JSON.stringify(data);
                        error.apiError = data; // Include the full API error response
                        
                        console.log(`[ApiClient] [${requestId}] Created error object with status:`, {
                            status: error.status,
                            statusText: error.statusText,
                            message: error.message,
                            endpoint: error.endpoint
                        });
                        
                        throw error;
                    }

                    // For auth endpoints, return the full response object
                    // For other endpoints, return data.data if it exists, otherwise return the full data
                    let responseData;
                    if (endpoint.includes('/auth')) {
                        responseData = data; // Return full response for auth endpoints
                        console.log(`[ApiClient] [${requestId}] Returning full auth response:`, JSON.stringify(responseData, null, 2));
                    } else {
                        responseData = data.data !== undefined ? data.data : data;
                    }

                    // Update cache only if enabled
                    if (this.cacheEnabled) {
                        this.cache.set(requestKey, {
                            data: responseData,
                            timestamp: Date.now()
                        });
                        debug(`[${requestId}] Updated cache for ${endpoint}`);
                    }

                    return responseData;

                } catch (error) {
                    // Check if this was a timeout error
                    const isTimeoutError = wasAbortedDueToTimeout ||
                        (error.name === 'AbortError' && !signal.aborted) ||
                        error.message.includes('timeout');

                    // Log the error type for debugging
                    debug(`[${requestId}] Request error (attempt ${attempt + 1}/${this.retryCount}): ${isTimeoutError ? 'Timeout' : 'Network'} error`, {
                        errorName: error.name,
                        errorMessage: error.message,
                        wasAbortedDueToTimeout,
                        signalAborted: signal.aborted,
                        timeout,
                        endpoint
                    });

                    // For timeout errors with a large timeout value, we might want to stop retrying
                    // as it's likely a server issue rather than a network issue
                    if (isTimeoutError && timeout > 30000 && attempt > 0) {
                        debug(`[${requestId}] Not retrying timeout with large timeout value (${timeout}ms)`);

                        // Store failed request for potential independent retry
                        this.failedRequests.set(requestKey, { endpoint, options });
                        debug(`[${requestId}] Added to failedRequests for potential manual retry`);

                        // Only show network error alert if it was our timeout that caused it
                        if (wasAbortedDueToTimeout) {
                            this.showNetworkErrorAlert(endpoint, error, {
                                wasAbortedDueToTimeout,
                                timeout
                            });
                        } else {
                            debug(`[${requestId}] Skipping alert for non-timeout abort`);
                        }

                        throw error;
                    }

                    attempt++;
                    if (attempt >= this.retryCount) {
                        debug(`[${requestId}] Request failed after ${this.retryCount} attempts:`, {
                            error: error.message,
                            endpoint,
                            timeout,
                            isTimeoutError,
                            wasAbortedDueToTimeout
                        });

                        // Store failed request for potential independent retry
                        this.failedRequests.set(requestKey, { endpoint, options });
                        debug(`[${requestId}] Added to failedRequests for potential manual retry`);

                        // Show network error alert to user only if:
                        // 1. It's not a duplicated request OR it's the original request that others are waiting on
                        // 2. skipErrorAlert is not set to true
                        // 3. It was aborted due to our timeout OR it's a non-abort error
                        const shouldShowAlert =
                            (!this.pendingRequests.has(requestKey) || this.pendingRequests.get(requestKey) === wrappedPromise) &&
                            options.skipErrorAlert !== true &&
                            (wasAbortedDueToTimeout || error.name !== 'AbortError');

                        if (shouldShowAlert) {
                            debug(`[${requestId}] Showing network error alert for ${endpoint}`);
                            this.showNetworkErrorAlert(endpoint, error, {
                                wasAbortedDueToTimeout,
                                timeout,
                                forceShow: options.forceShowAlert === true
                            });
                        } else {
                            debug(`[${requestId}] Skipping error alert for ${endpoint}`, {
                                isDuplicate: this.pendingRequests.has(requestKey),
                                skipErrorAlert: options.skipErrorAlert === true,
                                wasAbortedDueToTimeout,
                                isAbortError: error.name === 'AbortError'
                            });
                        }

                        throw error;
                    }

                    // Use exponential backoff for retry delay
                    const retryDelay = this.retryDelay * Math.pow(2, attempt);
                    debug(`[${requestId}] Retrying in ${retryDelay}ms... (attempt ${attempt + 1}/${this.retryCount})`);

                    await new Promise(resolve =>
                        setTimeout(resolve, retryDelay)
                    );
                }
            }
        })();

        // Create a wrapper promise that cleans up the pending request map when done
        const wrappedPromise = requestPromise
            .then(result => {
                // Clean up the pending request on success
                this.pendingRequests.delete(requestKey);
                debug(`[${requestId}] Request completed successfully: ${endpoint}`);
                return result;
            })
            .catch(error => {
                // Clean up the pending request on error
                this.pendingRequests.delete(requestKey);
                debug(`[${requestId}] Request failed with error: ${error.message}`);
                throw error;
            });

        // Store the wrapped promise in the pending requests map
        if (this.deduplicationEnabled) {
            this.pendingRequests.set(requestKey, wrappedPromise);
            debug(`[${requestId}] Added to pendingRequests map (size: ${this.pendingRequests.size})`);
        }

        return wrappedPromise;
    }

    // Cache and request deduplication management

    /**
     * Clear all cache entries
     */
    clearAllCache() {
        this.cache.clear();
        console.log('[ApiClient] Cleared all cache entries');
    }

    /**
     * Clear all pending requests
     */
    clearAllPendingRequests() {
        this.pendingRequests.clear();
        console.log('[ApiClient] Cleared all pending requests');
    }

    /**
     * Clear all failed requests
     */
    clearAllFailedRequests() {
        this.failedRequests.clear();
        console.log('[ApiClient] Cleared all failed requests');
    }

    /**
     * Clear everything - cache, pending requests, and failed requests
     */
    clearAll() {
        this.clearAllCache();
        this.clearAllPendingRequests();
        this.clearAllFailedRequests();
        console.log('[ApiClient] Cleared all data');
    }

    /**
     * Clear cache for a specific endpoint
     * @param {string} endpoint - The endpoint to clear cache for
     */
    clearCacheForEndpoint(endpoint) {
        if (!endpoint) return;

        let count = 0;
        Array.from(this.cache.keys())
            .filter(key => key.startsWith(endpoint))
            .forEach(key => {
                this.cache.delete(key);
                count++;
            });

        console.log(`[ApiClient] Cleared ${count} cache entries for endpoint: ${endpoint}`);
    }

    /**
     * Clear pending requests for a specific endpoint
     * @param {string} endpoint - The endpoint to clear pending requests for
     */
    clearPendingRequestsForEndpoint(endpoint) {
        if (!endpoint) return;

        let count = 0;
        Array.from(this.pendingRequests.keys())
            .filter(key => key.startsWith(endpoint))
            .forEach(key => {
                this.pendingRequests.delete(key);
                count++;
            });

        console.log(`[ApiClient] Cleared ${count} pending requests for endpoint: ${endpoint}`);
    }

    /**
     * Clear failed requests for a specific endpoint
     * @param {string} endpoint - The endpoint to clear failed requests for
     */
    clearFailedRequestsForEndpoint(endpoint) {
        if (!endpoint) return;

        let count = 0;
        Array.from(this.failedRequests.keys())
            .filter(key => key.startsWith(endpoint))
            .forEach(key => {
                this.failedRequests.delete(key);
                count++;
            });

        console.log(`[ApiClient] Cleared ${count} failed requests for endpoint: ${endpoint}`);
    }

    /**
     * Clear cache and requests for a specific endpoint
     * @param {string} endpoint - The endpoint to clear
     */
    clearForEndpoint(endpoint) {
        if (!endpoint) return;

        this.clearCacheForEndpoint(endpoint);
        this.clearPendingRequestsForEndpoint(endpoint);
        this.clearFailedRequestsForEndpoint(endpoint);
        console.log(`[ApiClient] Cleared all data for endpoint: ${endpoint}`);
    }

    /**
     * Clear cache by specific request key
     * @param {string} requestKey - The exact request key to clear
     */
    clearCacheByKey(requestKey) {
        if (this.cache.has(requestKey)) {
            this.cache.delete(requestKey);
            console.log(`[ApiClient] Cleared cache for key: ${requestKey}`);
            return true;
        }
        return false;
    }

    /**
     * Legacy method for backward compatibility
     * @param {string} endpoint - The endpoint to clear cache for
     */
    clearCache(endpoint) {
        if (endpoint) {
            this.clearForEndpoint(endpoint);
        } else {
            this.clearAll();
        }
    }

    /**
     * Enable request deduplication
     */
    enableDeduplication() {
        this.deduplicationEnabled = true;
        console.log('[ApiClient] Request deduplication enabled');
    }

    /**
     * Disable request deduplication
     */
    disableDeduplication() {
        this.deduplicationEnabled = false;
        console.log('[ApiClient] Request deduplication disabled');
    }

    /**
     * Get the current count of pending requests
     * @returns {number} - Number of pending requests
     */
    getPendingRequestCount() {
        return this.pendingRequests.size;
    }

    /**
     * Get information about the current deduplication status
     * @returns {Object} - Deduplication status information
     */
    getDeduplicationStatus() {
        return {
            enabled: this.deduplicationEnabled,
            pendingRequestCount: this.pendingRequests.size,
            pendingRequests: Array.from(this.pendingRequests.keys()),
            cacheEnabled: this.cacheEnabled,
            cacheSize: this.cache.size
        };
    }

    // Specific API methods
    async getExams(options = {}) {
        const endpoint = '/exam';

        // Generate the request key to check if this is likely to be deduplicated
        const requestKey = this.generateRequestKey(endpoint, {
            ...options,
            timeout: 15000
        });

        // Log if this request might be deduplicated
        if (this.deduplicationEnabled && this.pendingRequests.has(requestKey)) {
            console.log(`[ApiClient] Exams request will be deduplicated`);
            console.log(`[ApiClient] Current pending requests: ${this.pendingRequests.size}`);
        } else {
            console.log(`[ApiClient] Fetching exams list`);
        }

        return this.request(endpoint, {
            ...options,
            timeout: 15000 // Longer timeout for initial load
        });
    }

    async getQnA(examCode, options = {}, is_free = false) {
        // Generate a unique ID for this QnA request
        const qnaRequestId = Math.random().toString(36).substring(2, 8);

        // Special handling for logout flow - always allow is_free=true requests and bypass deduplication
        if (is_free === true) {
            console.log(`[QNATRACKER] [${qnaRequestId}] LOGOUT FLOW: Allowing is_free=true request for ${examCode}`);
            debug(`[${qnaRequestId}] [QNATRACKER] Allowing is_free=true request for ${examCode} (logout flow)`);

            // Clear any existing cache for this exam to ensure we get fresh content
            const params = new URLSearchParams({
                exam_code: examCode,
                is_free: 'false' // Clear premium content cache
            });
            const premiumEndpoint = `/qna?${params}`;
            if (this.cache.has(premiumEndpoint)) {
                console.log(`[QNATRACKER] [${qnaRequestId}] LOGOUT FLOW: Clearing premium content cache for ${examCode}`);
                this.cache.delete(premiumEndpoint);
            }

            // Also check for stale user data in AsyncStorage
            try {
                const userJson = await AsyncStorage.getItem('user');
                if (userJson) {
                    console.log(`[QNATRACKER] [${qnaRequestId}] LOGOUT FLOW: Found stale user data in AsyncStorage, removing it`);
                    await AsyncStorage.removeItem('user');

                    // Also try to parse the user ID to remove purchases
                    try {
                        const storedUser = JSON.parse(userJson);
                        if (storedUser && storedUser.id) {
                            const purchasesKey = `purchases_${storedUser.id}`;
                            console.log(`[QNATRACKER] [${qnaRequestId}] LOGOUT FLOW: Removing stale purchases with key: ${purchasesKey}`);
                            await AsyncStorage.removeItem(purchasesKey);
                        }
                    } catch (parseError) {
                        console.error(`[QNATRACKER] [${qnaRequestId}] LOGOUT FLOW: Error parsing stale user data:`, parseError);
                    }
                }
            } catch (error) {
                console.error(`[QNATRACKER] [${qnaRequestId}] LOGOUT FLOW: Error checking/removing stale user data:`, error);
            }
        }
        // Check if we've made this exact request recently (within 10 seconds)
        // This helps prevent duplicate requests during login flow
        else if (this.qnaRequestTracker.check(examCode, is_free, 10000)) {
            console.log(`[QNATRACKER] [${qnaRequestId}] DEDUPLICATION ACTIVE: Skipping duplicate QnA request for ${examCode}, is_free=${is_free} - found in tracker`);
            debug(`[${qnaRequestId}] [QNATRACKER] Skipping duplicate QnA request for ${examCode}, is_free=${is_free} - found in tracker`);

            // Return the cached data if available
            const params = new URLSearchParams({
                exam_code: examCode,
                is_free: is_free ? 'true' : 'false'
            });
            const endpoint = `/qna?${params}`;
            const requestKey = this.generateRequestKey(endpoint, {
                ...options,
                timeout: options.timeout || this.defaultTimeout
            });

            if (this.cacheEnabled && this.cache.has(requestKey)) {
                console.log(`[QNATRACKER] [${qnaRequestId}] USING CACHE: Returning cached data for duplicate QnA request`);
                debug(`[${qnaRequestId}] [QNATRACKER] Returning cached data for duplicate QnA request`);
                return this.cache.get(requestKey).data;
            }

            // If we don't have cached data, we'll need to make the request anyway
            console.log(`[QNATRACKER] [${qnaRequestId}] CACHE MISS: No cached data available for duplicate QnA request, proceeding with request`);
            debug(`[${qnaRequestId}] [QNATRACKER] No cached data available for duplicate QnA request, proceeding with request`);
        } else {
            console.log(`[QNATRACKER] [${qnaRequestId}] NEW REQUEST: First request for ${examCode}, is_free=${is_free} in tracking window`);
        }

        // Add this request to the tracker
        this.qnaRequestTracker.add(examCode, is_free);
        console.log(`[QNATRACKER] [${qnaRequestId}] ADDED TO TRACKER: ${examCode}, is_free=${is_free}`);

        const params = new URLSearchParams({
            exam_code: examCode,
            is_free: is_free ? 'true' : 'false'
        });

        debug(`[${qnaRequestId}] Fetching QnA with is_free=${is_free}, params=${params}`, {
            examCode,
            timeout: options.timeout || this.defaultTimeout,
            pendingRequestsCount: this.pendingRequests.size
        });

        // Use the default timeout from the class unless specified in options
        const timeout = options.timeout || this.defaultTimeout;

        // Generate the request key to check if this is likely to be deduplicated
        const endpoint = `/qna?${params}`;
        const requestKey = this.generateRequestKey(endpoint, {
            ...options,
            timeout
        });

        // Log if this request might be deduplicated
        if (this.deduplicationEnabled && this.pendingRequests.has(requestKey)) {
            debug(`[${qnaRequestId}] QnA request will be deduplicated: ${examCode}, is_free=${is_free}`, {
                requestKey,
                pendingRequestsCount: this.pendingRequests.size,
                pendingKeys: Array.from(this.pendingRequests.keys())
            });
        }

        return this.request(endpoint, {
            ...options,
            timeout
        });
    }

    /**
     * Clear the QnA request tracker
     */
    clearQnARequestTracker() {
        this.qnaRequestTracker.clear();
        console.log('[ApiClient] Cleared QnA request tracker');
    }

    async getAIResponse(payload, options = {}) {
        try {
            // Validate required fields
            if (!payload.userQuery && !payload.question) {
                throw new Error('Either userQuery or question must be provided');
            }

            // Format the payload with required question field
            const formattedPayload = {
                question: payload.question || payload.userQuery, // Required field
                ...payload
            };

            // Remove duplicate fields
            delete formattedPayload.userQuery;

            console.log('[ApiClient] Formatted AI payload:', JSON.stringify(formattedPayload));

            // For AI requests, we might want to disable deduplication since each request
            // could be unique even with the same payload (e.g., different AI responses)
            const shouldDeduplicateAI = options.deduplicateAI !== false && this.deduplicationEnabled;

            // AI requests might need a longer timeout, but respect the provided timeout
            const aiTimeout = options.timeout || Math.max(this.defaultTimeout, 20000);
            console.log(`[ApiClient] Using timeout of ${aiTimeout}ms for AI request`);

            // Use the standard request method with the appropriate timeout
            // This ensures consistent timeout handling across all request types
            const response = await this.request('/ai/askAiSimple', {
                method: 'POST',
                body: JSON.stringify(formattedPayload),
                timeout: aiTimeout,
                deduplicationEnabled: shouldDeduplicateAI,
                // Don't show the generic network error alert for AI requests
                // We'll show a more specific one in the catch block
                skipErrorAlert: true,
                ...options,
            });

            console.log('[ApiClient] Raw AI response:', JSON.stringify(response));
            return response;
        } catch (error) {
            console.error('[ApiClient] AI request failed:', error);

            // Show a specific error message for AI requests only if not skipped
            if ((error.name === 'AbortError' || error.message.includes('timeout')) && !options.skipTimeoutAlert) {
                // Only show the alert if we're not already showing too many alerts
                if (!this._isShowingTooManyAlerts()) {
                    Alert.alert(
                        'AI Response Timeout',
                        'The AI is taking longer than expected to respond. Please try again with a simpler question.',
                        [{ text: 'OK', onPress: () => console.log('AI timeout alert closed') }]
                    );
                }
            }

            throw error;
        }
    }

    /**
     * User Authentication and Profile Management
     */

    /**
     * Authenticate user with Google ID token
     * @param {string} googleIdToken - Google ID token from Google Sign-In
     * @returns {Promise<Object>} - User data including authentication token
     */
    async loginWithGoogle(googleIdToken) {
        console.log('[ApiClient] Logging in with Google, token length:', googleIdToken ? googleIdToken.length : 0);
        debug('Logging in with Google');

        if (!googleIdToken) {
            console.error('[ApiClient] No Google ID token provided to loginWithGoogle');
            throw new Error('No Google ID token provided');
        }

        try {
            console.log('[ApiClient] Sending request to /auth/google endpoint');
            const response = await this.request('/auth/google', {
                method: 'POST',
                body: JSON.stringify({ idToken: googleIdToken }),
                skipCache: true
            });

            console.log('[ApiClient] Received response from /auth/google:', JSON.stringify(response, null, 2));
            console.log('response:', response);
            console.log('response type:', typeof response);
            console.log('response is null:', response === null);
            console.log('response is undefined:', response === undefined);

            // Check if we got a valid response
            if (!response) {
                console.error('[ApiClient] Received null/undefined response from server');
                throw new Error('No response received from server');
            }

            // Check if the response indicates success
            if (response.success === false) {
                console.error('[ApiClient] Server returned error:', response.message || 'Unknown error');
                throw new Error(response.message || 'Authentication failed');
            }

            // If login is successful, set the authentication token
            if (response && response.token) {
                // Log the token details for debugging
                console.log('[ApiClient] Auth token found in response, type:', typeof response.token,
                    'length:', response.token ? response.token.length : 0);

                // Make sure the token is a string
                const tokenStr = String(response.token);

                // Set the auth token
                this.setAuthToken(tokenStr);

                // Also store the token in AsyncStorage for persistence
                try {
                    await AsyncStorage.setItem('auth_token', tokenStr);
                    console.log('[ApiClient] Auth token stored in AsyncStorage');
                } catch (storageError) {
                    console.error('[ApiClient] Failed to store auth token in AsyncStorage:', storageError);
                }

                console.log('[ApiClient] Auth token set after Google login, token length:', tokenStr.length);
                debug('Auth token set after Google login');
            } else {
                console.error('[ApiClient] No auth token received from Google login');
                console.error('[ApiClient] Response structure:', {
                    hasResponse: !!response,
                    responseKeys: response ? Object.keys(response) : [],
                    hasToken: response && 'token' in response,
                    tokenValue: response ? response.token : 'N/A'
                });
                debug('No auth token received from Google login');
                throw new Error('Google login failed - no token in response');
            }

            return response;
        } catch (error) {
            console.error('[ApiClient] Google login failed with error:', error);
            debug('Google login failed:', error);
            throw error;
        }
    }

    /**
     * Logout user and invalidate token on server
     * @returns {Promise<Object>} - Logout confirmation
     */
    async logout() {
        debug('Logging out user');
        try {
            // Only attempt to call the logout endpoint if we have a custom auth token
            if (this.token !== API_TOKEN) {
                await this.request('/auth/logout', {
                    method: 'POST',
                    skipCache: true
                });
            }
        } catch (error) {
            debug('Error during logout API call:', error);
            // Continue with logout even if API call fails
        } finally {
            // Always reset to the default API token
            this.setAuthToken(null);

            // Also remove the token from AsyncStorage
            try {
                AsyncStorage.removeItem('auth_token');
                console.log('[ApiClient] Auth token removed from AsyncStorage during logout');
            } catch (storageError) {
                console.error('[ApiClient] Failed to remove auth token from AsyncStorage:', storageError);
            }
        }

        return { success: true };
    }

    /**
     * Get current user profile
     * @returns {Promise<Object>} - User profile data
     */
    async getUserProfile() {
        debug('Getting user profile');
        return this.request('/user/profile', {
            method: 'GET'
        });
    }

    /**
   * Get user by Google ID (requires JWT authentication)
   * @param {string} googleId - Google ID of the user
   * @returns {Promise<Object>} - Complete user object
   */
    async getUserByGoogleId(googleId) {
        debug(`Getting user by Google ID: ${googleId}`);
        if (!googleId) {
            throw new Error('Google ID is required');
        }
        return this.request(`/user/google/${encodeURIComponent(googleId)}`, {
            method: 'GET'
        });
    }

    /**
     * Update user profile
     * @param {Object} profileData - Profile data to update
     * @returns {Promise<Object>} - Updated user profile
     */
    async updateUserProfile(profileData) {
        debug('Updating user profile');
        return this.request('/user/profile', {
            method: 'PUT',
            body: JSON.stringify(profileData),
            skipCache: true
        });
    }

    /**
     * User Progress Context
     */

    /**
     * Get user progress for all courses or a specific course
     * @param {string} [courseId] - Optional course ID to filter progress
     * @returns {Promise<Object>} - User progress data
     */
    async getUserProgress(courseId) {
        const endpoint = courseId
            ? `/user/progress?courseId=${encodeURIComponent(courseId)}`
            : '/user/progress';

        debug(`Getting user progress${courseId ? ` for course ${courseId}` : ''}`);
        return this.request(endpoint, {
            method: 'GET'
        });
    }

    /**
     * Update user progress for exam questions
     * @param {string} examId - Exam ID (e.g., AIF-C01)
     * @param {string} subject - Subject name
     * @param {Object} progressData - Progress data to update
     * @param {string} progressData.questionId - Question ID
     * @param {string} progressData.action - Action type: 'browsed', 'bookmarked', or 'answered'
     * @param {boolean} [progressData.isBookmarked] - Required when action is 'bookmarked'
     * @param {boolean} [progressData.isCorrect] - Required when action is 'answered'
     * @returns {Promise<Object>} - Updated user progress
     */
    async updateUserProgress(examId, subject, progressData) {
        debug(`Updating user progress for exam ${examId}, subject ${subject}`);
        return this.request(`/user/progress/${encodeURIComponent(examId)}/${encodeURIComponent(subject)}`, {
            method: 'PUT',
            body: JSON.stringify(progressData),
            skipCache: true
        });
    }

    /**
     * Bulk update user progress for an exam according to new Swagger specification
     * Uses PUT /api/user/progress/{examId} endpoint
     * @param {string} examId - Exam ID
     * @param {Object} progressData - Progress data organized by subject in UserProgressContext format
     * @returns {Promise<Object>} - Updated progress data
     */
    async updateUserProgressBulk(examId, progressData) {
        // Validate required parameters
        if (!examId) {
            throw new Error('examId is required for bulk progress update');
        }

        if (!progressData || typeof progressData !== 'object') {
            throw new Error('progressData must be a valid object');
        }

        debug(`Bulk updating user progress for exam ${examId}`, {
            subjects: Object.keys(progressData).length,
            progressDataKeys: Object.keys(progressData)
        });

        // The progressData should already be in the correct format:
        // {
        //   "subject1": { browsed: [...], bookmarked: [...], correct: [...], incorrect: [...] },
        //   "subject2": { browsed: [...], bookmarked: [...], correct: [...], incorrect: [...] }
        // }

        const endpoint = `/user/progress/${encodeURIComponent(examId)}`;

        return this.request(endpoint, {
            method: 'PUT',
            body: JSON.stringify(progressData),
            skipCache: true,
            timeout: 10000 // 10 second timeout for bulk operations
        });
    }

    /**
     * Quiz Result Context
     */

    /**
     * Get user quiz results
     * @param {string} [quizId] - Optional quiz ID to filter results
     * @returns {Promise<Object>} - User quiz results
     */
    async getQuizResults(quizId) {
        const endpoint = quizId
            ? `/user/quiz-results?quizId=${encodeURIComponent(quizId)}`
            : '/user/quiz-results';

        debug(`Getting user quiz results${quizId ? ` for quiz ${quizId}` : ''}`);
        return this.request(endpoint, {
            method: 'GET'
        });
    }

    /**
     * Submit a quiz result
     * @param {Object} quizResult - Quiz result data
     * @param {string} quizResult.quizId - Quiz ID
     * @param {number} quizResult.score - Score as percentage
     * @param {number} quizResult.totalQuestions - Total number of questions
     * @param {number} quizResult.correctAnswers - Number of correct answers
     * @param {number} quizResult.timeTaken - Time taken in seconds
     * @param {Array} quizResult.answers - Array of answer objects
     * @returns {Promise<Object>} - Saved quiz result
     */
    async submitQuizResult(quizResult) {
        debug(`Submitting quiz result for quiz ${quizResult.quizId}`);
        return this.request('/user/quiz-results', {
            method: 'POST',
            body: JSON.stringify(quizResult),
            skipCache: true
        });
    }

    /**
     * Purchase Context
     */

    /**
     * Get user purchases
     * @param {string} [status] - Optional status filter ('active', 'expired', 'refunded')
     * @returns {Promise<Object>} - User purchases
     */
    async getUserPurchases(status) {
        const endpoint = status
            ? `/user/purchases?status=${encodeURIComponent(status)}`
            : '/user/purchases';

        debug(`Getting user purchases${status ? ` with status ${status}` : ''}`);

        try {
            return await this.request(endpoint, {
                method: 'GET'
            });
        } catch (error) {
            // Check if this is an authentication error
            if (error.message.includes('401') || error.message.includes('403')) {
                console.error('[ApiClient] Authentication error in getUserPurchases - token may be expired:', {
                    currentTokenLength: this.token ? this.token.length : 0,
                    isDefaultToken: this.token === API_TOKEN,
                    endpoint: endpoint,
                    error: error.message
                });

                // Try to refresh the token from AsyncStorage
                try {
                    const storedGoogleToken = await AsyncStorage.getItem('google_id_token');
                    if (storedGoogleToken && storedGoogleToken !== this.token) {
                        console.log('[ApiClient] Found different Google token in storage, updating auth token');
                        this.setAuthToken(storedGoogleToken);

                        // Retry the request with the refreshed token
                        return await this.request(endpoint, {
                            method: 'GET'
                        });
                    }
                } catch (refreshError) {
                    console.error('[ApiClient] Failed to refresh token from storage:', refreshError);
                }
            }

            throw error;
        }
    }

    /**
     * Verify and record a purchase
     * @param {Object} purchaseData - Purchase data
     * @param {string} purchaseData.productId - Product ID
     * @param {string} purchaseData.transactionId - Transaction ID
     * @param {string} purchaseData.platform - Platform ('ios', 'android', 'web')
     * @param {string} purchaseData.receipt - Purchase receipt for verification
     * @returns {Promise<Object>} - Verified purchase data
     */
    async verifyPurchase(purchaseData) {
        debug(`Verifying purchase for product ${purchaseData.productId}`);
        return this.request('/user/purchases/verify', {
            method: 'POST',
            body: JSON.stringify(purchaseData),
            skipCache: true
        });
    }

    /**
     * AI Credit Context
     */

    /**
     * Get user AI credits
     * @returns {Promise<Object>} - User AI credits data
     */
    async getAICredits() {
        debug('Getting user AI credits');
        return this.request('/user/ai-credits', {
            method: 'GET'
        });
    }

    /**
     * Get AI credit transaction history
     * @param {number} [limit=20] - Number of transactions to return
     * @param {number} [offset=0] - Offset for pagination
     * @returns {Promise<Object>} - AI credit transactions
     */
    async getAICreditTransactions(limit = 20, offset = 0) {
        debug(`Getting AI credit transactions (limit: ${limit}, offset: ${offset})`);
        return this.request(`/user/ai-credits/transactions?limit=${limit}&offset=${offset}`, {
            method: 'GET'
        });
    }

    /**
     * Purchase AI credits
     * @param {Object} purchaseData - Purchase data
     * @param {number} purchaseData.amount - Amount of credits to purchase
     * @param {string} purchaseData.paymentMethod - Payment method
     * @param {Object} purchaseData.paymentDetails - Payment details
     * @returns {Promise<Object>} - Updated AI credits
     */
    async purchaseAICredits(purchaseData) {
        debug(`Purchasing ${purchaseData.amount} AI credits`);
        return this.request('/user/ai-credits/purchase', {
            method: 'POST',
            body: JSON.stringify(purchaseData),
            skipCache: true
        });
    }

    /**
     * AI Chat Context
     */

    /**
     * Get user AI chat history
     * Returns the complete AI chat history organized by question IDs
     * @returns {Promise<Object>} - Complete AI chat history in the format:
     * {
     *   "questionId1": {
     *     "messages": [
     *       {
     *         "id": "message-id",
     *         "choices": [
     *           {
     *             "index": 0,
     *             "message": {
     *               "role": "user|assistant",
     *               "content": "message content",
     *               "isLoading": false,
     *               "metadata": { ... }
     *             },
     *             "logprobs": null,
     *             "finish_reason": "stop"
     *           }
     *         ]
     *       }
     *     ],
     *     "quickReplies": ["reply1", "reply2", "reply3"]
     *   }
     * }
     */
    async getAIChatHistory() {
        debug('Getting complete AI chat history');
        return this.request('/user/ai-chats', {
            method: 'GET'
        });
    }

    /**
     * Update user AI chat history
     * Replaces the entire AI chat history with the provided data
     * @param {Object} chatHistoryData - Complete AI chat history data organized by question IDs
     * @param {Object} chatHistoryData[questionId] - Chat data for a specific question
     * @param {Array} chatHistoryData[questionId].messages - Array of message objects
     * @param {Array} chatHistoryData[questionId].quickReplies - Array of quick reply strings
     * @returns {Promise<Object>} - Updated AI chat history
     * @example
     * // Example usage:
     * const chatHistory = {
     *   "679e05c962fb0d0bbe484926": {
     *     "messages": [
     *       {
     *         "id": "welcome-message",
     *         "choices": [
     *           {
     *             "index": 0,
     *             "message": {
     *               "role": "assistant",
     *               "content": "I'm here to help with this Q&A question. What would you like to know?",
     *               "isLoading": false
     *             }
     *           }
     *         ]
     *       }
     *     ],
     *     "quickReplies": [
     *       "Explain why the answer is correct and others are wrong.",
     *       "Give an example to clarify the answer.",
     *       "Share references for the correct answer."
     *     ]
     *   }
     * };
     * await apiClient.updateAIChatHistory(chatHistory);
     *
     * // For incremental updates, use addAIChatHistory instead:
     * await apiClient.addAIChatHistory(examId, qnaId, messages, quickReplies);
     */
    async updateAIChatHistory(chatHistoryData) {
        debug('Updating complete AI chat history');
        return this.request('/user/ai-chats', {
            method: 'PUT',
            body: JSON.stringify(chatHistoryData),
            skipCache: true
        });
    }

    /**
     * Get a specific AI chat
     * @param {string} chatId - Chat ID
     * @returns {Promise<Object>} - Chat data including messages
     */
    async getAIChat(chatId) {
        debug(`Getting AI chat ${chatId}`);
        return this.request(`/user/ai-chats/${encodeURIComponent(chatId)}`, {
            method: 'GET'
        });
    }

    /**
     * Create a new AI chat
     * @param {string} [title] - Optional chat title
     * @returns {Promise<Object>} - New chat data
     */
    async createAIChat(title) {
        debug('Creating new AI chat');
        return this.request('/user/ai-chats', {
            method: 'POST',
            body: JSON.stringify({ title }),
            skipCache: true
        });
    }

    /**
     * Send a message in an AI chat
     * @param {string} chatId - Chat ID
     * @param {string} message - User message
     * @returns {Promise<Object>} - Updated chat with AI response
     */
    async sendAIChatMessage(chatId, message) {
        debug(`Sending message to AI chat ${chatId}`);
        return this.request(`/user/ai-chats/${encodeURIComponent(chatId)}/messages`, {
            method: 'POST',
            body: JSON.stringify({ message }),
            skipCache: true,
            timeout: 30000 // Longer timeout for AI responses
        });
    }

    /**
     * Delete an AI chat
     * @param {string} chatId - Chat ID
     * @returns {Promise<Object>} - Deletion confirmation
     */
    async deleteAIChat(chatId) {
        debug(`Deleting AI chat ${chatId}`);
        return this.request(`/user/ai-chats/${encodeURIComponent(chatId)}`, {
            method: 'DELETE',
            skipCache: true
        });
    }

    /**
     * Login Context
     */

    /**
     * Get user login history
     * @param {number} [limit=10] - Number of login records to return
     * @param {number} [offset=0] - Offset for pagination
     * @returns {Promise<Object>} - User login history
     */
    async getLoginHistory(limit = 10, offset = 0) {
        debug(`Getting login history (limit: ${limit}, offset: ${offset})`);
        return this.request(`/user/login-history?limit=${limit}&offset=${offset}`, {
            method: 'GET'
        });
    }

    /**
     * Add messages to AI chat history for a specific exam and QnA
     * @param {string} examId - Exam ID (e.g., "aws-certified-ai-practitioner-aif-c01")
     * @param {string} qnaId - QnA ID (e.g., "679e05c962fb0d0bbe484926")
     * @param {Array} messages - Array of message objects to append
     * @param {Array} quickReplies - Optional array of quick reply suggestions
     * @returns {Promise} API response with message count information
     */
    async addAIChatHistory(examId, qnaId, messages, quickReplies = null) {
        debug(`Adding AI chat messages for exam: ${examId}, qna: ${qnaId}, messageCount: ${messages?.length || 0}`);

        try {
            // Validate required parameters
            if (!examId || typeof examId !== 'string') {
                throw new Error('examId is required and must be a string');
            }

            if (!qnaId || typeof qnaId !== 'string') {
                throw new Error('qnaId is required and must be a string');
            }

            if (!messages || !Array.isArray(messages) || messages.length === 0) {
                throw new Error('messages is required and must be a non-empty array');
            }

            // Validate examId format
            if (!examId.match(/^[a-z0-9-]+$/)) {
                throw new Error('Invalid examId format. Expected format: aws-certified-ai-practitioner-aif-c01');
            }

            // Validate qnaId format
            if (!qnaId.match(/^[a-f0-9]{24}$/)) {
                throw new Error('Invalid qnaId format. Expected 24-character hexadecimal string');
            }

            // Validate message structure
            for (let i = 0; i < messages.length; i++) {
                const message = messages[i];
                if (!message.id || !message.choices || !Array.isArray(message.choices)) {
                    throw new Error(`Message at index ${i} must have id and choices array`);
                }

                for (let j = 0; j < message.choices.length; j++) {
                    const choice = message.choices[j];
                    if (!choice.message || !choice.message.role || !choice.message.content) {
                        throw new Error(`Choice at index ${j} in message ${i} must have message with role and content`);
                    }

                    if (!['user', 'assistant'].includes(choice.message.role)) {
                        throw new Error(`Message role must be either "user" or "assistant" in message ${i}, choice ${j}`);
                    }
                }
            }

            // Prepare request body
            const requestBody = {
                messages: messages
            };

            // Add quickReplies if provided
            if (quickReplies && Array.isArray(quickReplies)) {
                requestBody.quickReplies = quickReplies;
            }

            console.log(`[ApiClient] Sending ${messages.length} messages to AI chat history for ${examId}/${qnaId}`);

            const response = await this.request(`/user/ai-chats/${examId}/${qnaId}/messages`, {
                method: 'PUT',
                body: JSON.stringify(requestBody),
                skipCache: true
            });

            console.log(`[ApiClient] Successfully added ${messages.length} messages to AI chat history for ${examId}/${qnaId}`);

            return response;
        } catch (error) {
            console.error('[ApiClient] Error in addAIChatHistory:', error);
            throw error;
        }
    }


    // QnA Feedback
    /**
     * Submit feedback about a QnA item
     * @param {string} userId - ID of the user submitting feedback
     * @param {string} qnaId - ID of the QnA item being feedbacked
     * @param {string} content - Feedback content
     * @returns {Promise<Object>} - Response from server
     */
    async submitQnAFeedback(userId, qnaId, content) {
        debug(`Submitting QnA feedback for qnaId: ${qnaId}`);

        // Validate required parameters
        if (!userId || typeof userId !== 'string') {
            throw new Error('userId is required and must be a string');
        }
        if (!qnaId || typeof qnaId !== 'string') {
            throw new Error('qnaId is required and must be a string');
        }
        if (!content || typeof content !== 'string') {
            throw new Error('content is required and must be a string');
        }

        return this.request('/qna/feedback', {
            method: 'POST',
            body: JSON.stringify({ userId, qnaId, content }),
            skipCache: true,
            timeout: 10000 // 10 second timeout
        });
    }
}

const apiClient = new ApiClient();

export default apiClient;