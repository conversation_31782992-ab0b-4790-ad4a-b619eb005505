import Purchases, { LOG_LEVEL } from 'react-native-purchases';
import { NativeModules, Platform } from 'react-native';
import { REVENUECAT_API_KEY, REVENUECAT_REST_API_KEY, REVENUECAT_PROJECT_ID } from '@env';
import AsyncStorage from '@react-native-async-storage/async-storage';

/**
 * RevenueCat Service for handling in-app purchases
 * This service provides a clean interface for RevenueCat operations
 */
class RevenueCatService {

  /**
   * Initialize RevenueCat SDK
   * @param {string} apiKey - RevenueCat API key
   * @param {string} userId - Optional user ID for identification
   */
  static async initialize(userId = null, apiKey = REVENUECAT_API_KEY, maxRetries = 3) {
    try {
      console.log(apiKey)
      if (this.isInitialized) {
        console.log('[RevenueCat] Already initialized');
        return true;
      }

      if (!apiKey) {
        throw new Error('RevenueCat API key is required');
      }

      // Check if Purchases module is available
      if (!this.isAvailable()) {
        throw new Error('RevenueCat SDK is not available. Make sure react-native-purchases is properly installed and linked.');
      }

      this.apiKey = apiKey;

      console.log('[RevenueCat] Starting initialization with API key:', apiKey);

      let retryCount = 0;
      let lastError = null;

      while (retryCount < maxRetries) {
        try {
          console.log(`[RevenueCat] Initialization attempt ${retryCount + 1}/${maxRetries}`);

          console.log('[RevenueCat] Setting log level...');
          await Purchases.setLogLevel(LOG_LEVEL.DEBUG);

          /* // Get the app's package name/bundle ID for debugging
          const bundleId = Platform.OS === 'ios' 
            ? NativeModules.RNDeviceInfo?.getBundleId?.() || 'Unknown'
            : NativeModules.RNDeviceInfo?.getPackageName?.() || NativeModules.RNDeviceInfo?.getBundleId?.() || 'Unknown';
          console.log(NativeModules.RNDeviceInfo)
          console.log('[RevenueCat] App bundle ID/package name:', bundleId); */

          const configOptions = {
            apiKey: apiKey,
            /* appUserID: userId || 'debug_user_' + Date.now(),
            observerMode: false,
            diagnosticsEnabled: true */
          };

          console.log('[RevenueCat] Configuration options:', JSON.stringify(configOptions));
          await Purchases.configure(configOptions);

          console.log('[RevenueCat] Configuration completed');
          this.isInitialized = true;
          console.log('[RevenueCat] Marked as initialized');
          return true;

        } catch (configError) {
          console.warn(`[RevenueCat] Initialization attempt ${retryCount + 1} failed:`, configError);
          lastError = configError;

          // Don't retry for certain errors
          if (configError.message.includes('invalid API key') ||
            configError.message.includes('configuration')) {
            break;
          }

          // Wait before retrying
          if (retryCount < maxRetries - 1) {
            await new Promise(resolve => setTimeout(resolve, 1000 * (retryCount + 1)));
          }
          retryCount++;
        }
      }

      this.isInitialized = false;
      throw lastError || new Error('RevenueCat initialization failed after multiple attempts');

      // Identify user if provided (AFTER marking as initialized)
      if (userId) {
        try {
          console.log('[RevenueCat] Identifying user:', userId);
          await this.identifyUser(userId);
        } catch (identifyError) {
          console.warn('[RevenueCat] User identification failed, but continuing:', identifyError);
          // Don't throw here, as initialization can continue without user identification
        }
      }

      console.log('[RevenueCat] Initialized successfully');
      return true;
    } catch (error) {
      console.error('[RevenueCat] Initialization failed:', error);
      this.isInitialized = false;
      throw error;
    }
  }

  /**
   * Identify user with RevenueCat
   * @param {string} userId - User ID
   * @param {Object} attributes - Optional user attributes
   */
  static async identifyUser(userId, attributes = {}) {
    try {
      if (!this.isInitialized) {
        throw new Error('RevenueCat not initialized');
      }

      await Purchases.logIn(userId);

      // Set user attributes if provided
      if (Object.keys(attributes).length > 0) {
        await Purchases.setAttributes(attributes);
      }

      console.log('[RevenueCat] User identified:', userId);
      return true;
    } catch (error) {
      console.error('[RevenueCat] User identification failed:', error);
      throw error;
    }
  }

  /**
   * Check if RevenueCat is initialized, throw error if not
   * @private
   */
  static _checkInitialized() {
    if (!this.isInitialized) {
      throw new Error('RevenueCat not initialized. Call initialize() first.');
    }
    if (!this.apiKey) {
      throw new Error('RevenueCat API key is required');
    }
  }

  /**
   * Get available offerings
   * @returns {Object} Offerings object with enhanced error handling
   */
  static async getOfferings(maxRetries = 1) {
    try {
      this._checkInitialized();

      let retryCount = 0;
      let lastError = null;

      while (retryCount < maxRetries) {
        try {
          console.log(`[RevenueCat] Fetching offerings (attempt ${retryCount + 1}/${maxRetries})...`);
          const offerings = await Purchases.getOfferings();

          console.log('[RevenueCat] Raw offerings response:', JSON.stringify(offerings, null, 2));

          if (offerings.current !== null && offerings.current.availablePackages.length !== 0) {
            console.log('[RevenueCat] Current offering found:', offerings.current.identifier);
            console.log('[RevenueCat] Available packages:', offerings.current.availablePackages.length);
          } else {
            console.log('[RevenueCat] No current offering or packages found');
          }

          if (offerings.all && Object.keys(offerings.all).length > 0) {
            console.log('[RevenueCat] All available offerings:', Object.keys(offerings.all));
          } else {
            console.log('[RevenueCat] No offerings configured in dashboard');
          }

          return offerings;
        } catch (error) {
          console.error(`[RevenueCat] Offerings fetch attempt ${retryCount + 1} failed:`, error);
          lastError = error;

          // Don't retry for certain errors
          if (error.message.includes('invalid API key') ||
            error.message.includes('configuration')) {
            break;
          }

          // Wait before retrying
          if (retryCount < maxRetries - 1) {
            await new Promise(resolve => setTimeout(resolve, 1000 * (retryCount + 1)));
          }
          retryCount++;
        }
      }

      // If we get here, all retries failed
      const errorMessage = lastError?.message || 'Failed to get offerings after multiple attempts';
      console.error('[RevenueCat]', errorMessage);

      // Provide more detailed error information
      const detailedError = new Error(errorMessage);
      detailedError.code = lastError?.code || 'OFFERINGS_FETCH_FAILED';
      detailedError.retries = retryCount;
      detailedError.isFinalAttempt = retryCount >= maxRetries;

      throw detailedError;
    } catch (error) {
      console.error('[RevenueCat] Failed to get offerings:', error);
      throw error;
    }
  }

  /**
   * Display packages helper method according to RevenueCat documentation
   * @param {Object} offerings - Offerings object from getOfferings()
   * @returns {Object} Formatted package information for display
   */
  static displayPackages(offerings) {
    try {
      const result = {
        hasOfferings: false,
        currentOffering: null,
        packages: [],
        allOfferings: [],
        debugInfo: {}
      };

      if (!offerings) {
        console.log('[RevenueCat] No offerings provided to displayPackages');
        return result;
      }

      // Check current offering (following documentation pattern)
      if (offerings.current !== null && offerings.current.availablePackages.length !== 0) {
        result.hasOfferings = true;
        result.currentOffering = {
          identifier: offerings.current.identifier,
          description: offerings.current.serverDescription,
          metadata: offerings.current.metadata
        };

        // Process available packages
        result.packages = offerings.current.availablePackages.map((pkg, index) => ({
          index: index + 1,
          identifier: pkg.identifier,
          packageType: pkg.packageType,
          product: {
            identifier: pkg.product?.identifier,
            title: pkg.product?.title,
            description: pkg.product?.description,
            price: pkg.product?.price,
            priceString: pkg.product?.priceString,
            currencyCode: pkg.product?.currencyCode,
            subscriptionPeriod: pkg.product?.subscriptionPeriod,
            introPrice: pkg.product?.introPrice
          }
        }));

        console.log('[RevenueCat] Display packages - Current offering found with', result.packages.length, 'packages');
      } else {
        console.log('[RevenueCat] Display packages - No current offering or packages available');
      }

      // Process all offerings
      if (offerings.all && Object.keys(offerings.all).length > 0) {
        result.allOfferings = Object.keys(offerings.all).map(key => ({
          identifier: key,
          description: offerings.all[key].serverDescription,
          packagesCount: offerings.all[key].availablePackages.length
        }));
      }

      // Debug information
      result.debugInfo = {
        totalOfferings: Object.keys(offerings.all || {}).length,
        hasCurrentOffering: offerings.current !== null,
        currentOfferingPackages: offerings.current?.availablePackages?.length || 0
      };

      return result;
    } catch (error) {
      console.error('[RevenueCat] Error in displayPackages:', error);
      return {
        hasOfferings: false,
        currentOffering: null,
        packages: [],
        allOfferings: [],
        debugInfo: { error: error.message }
      };
    }
  }

  /**
   * Get customer info (subscription status) with caching
   * @param {boolean} isForceRefresh - Optional flag to bypass cache
   * @returns {Object} Customer info object
   */
  static async getCustomerInfo(isForceRefresh = false) {
    try {
      const CACHE_KEY = '@RevenueCat:customerInfo';
      const FIVE_MINUTES = 5 * 60 * 1000;

      // Check cache first if not forcing refresh
      if (!isForceRefresh) {
        try {
          const cachedData = await AsyncStorage.getItem(CACHE_KEY);
          if (cachedData) {
            const { data, timestamp } = JSON.parse(cachedData);
            const now = Date.now();
            const cacheAge = now - timestamp;
            
            if (cacheAge < FIVE_MINUTES) {
              console.log('[RevenueCat] Returning cached customer info');
              return data;
            }
          }
        } catch (cacheError) {
          console.warn('[RevenueCat] Error reading cache, proceeding with fresh request:', cacheError);
        }
      }
      console.log('[RevenueCat] Get fresh data from API');
      const customerInfo = await Purchases.getCustomerInfo();
      console.log('[RevenueCat] Customer info retrieved:', customerInfo);

      // Update cache
      try {
        await AsyncStorage.setItem(CACHE_KEY, JSON.stringify({
          data: customerInfo,
          timestamp: Date.now()
        }));
      } catch (cacheError) {
        console.warn('[RevenueCat] Error updating cache:', cacheError);
      }

      return customerInfo;
    } catch (error) {
      console.error('[RevenueCat] Failed to get customer info:', error);
      throw error;
    }
  }

  /**
   * Make a purchase
   * @param {Object} packageToPurchase - Package object from offerings
   * @returns {Object} Purchase result
   */
  static async makePurchase(packageToPurchase, upgradeInfo = null) {
    try {
      this._checkInitialized();
      console.log('[RevenueCat] Making purchase for upgradeInfo:', upgradeInfo);
      const { customerInfo, productIdentifier } = await Purchases.purchasePackage(
        packageToPurchase,
        googleProductChangeInfo = upgradeInfo
      );

      console.log('[RevenueCat] Purchase successful:', {
        productIdentifier,
        activeSubscriptions: customerInfo.activeSubscriptions || [],
        googleProductChangeInfo: upgradeInfo || null
      });

      return {
        success: true,
        customerInfo,
        productIdentifier,
        activeSubscriptions: customerInfo.activeSubscriptions || []
      };
    } catch (error) {
      console.log('[RevenueCat] Purchase failed:', error);

      // Handle specific error types
      if (error.code === 'PURCHASE_CANCELLED' ||
        error.code === 'USER_CANCELLED' ||
        error.message?.toLowerCase().includes('cancel') ||
        error.message?.toLowerCase().includes('user cancelled')) {
        return { success: false, cancelled: true, error: 'Purchase was cancelled' };
      }

      return { success: false, cancelled: false, error: error.message };
    }
  }

  /**
   * Restore purchases
   * @returns {Object} Restore result
   */
  static async restorePurchases() {
    try {
      this._checkInitialized();

      const customerInfo = await Purchases.restorePurchases();
      console.log('[RevenueCat] Purchases restored:', customerInfo);
      return customerInfo;
    } catch (error) {
      console.error('[RevenueCat] Failed to restore purchases:', error);
      throw error;
    }
  }

  /**
   * Logout user
   */
  static async logout() {
    try {
      this._checkInitialized();

      await Purchases.logOut();
      console.log('[RevenueCat] User logged out');
    } catch (error) {
      console.error('[RevenueCat] Logout failed:', error);
      throw error;
    }
  }

  /**
   * Get products from RevenueCat API using REST API v2
   * @param {Object} options - Optional parameters for the API request
   * @param {number} options.limit - Maximum number of products to return (default: 20)
   * @param {string} options.startingAfter - Cursor for pagination
   * @returns {Object} Products list from the REST API
   */
  static async getProductsViaRestApi(options = {}) {
    try {
      /* if (!this.isInitialized) {
        throw new Error('RevenueCat not initialized');
      } */

      // Check if project ID is available from environment variables
      if (!REVENUECAT_PROJECT_ID) {
        throw new Error('REVENUECAT_PROJECT_ID is not defined in environment variables');
      }

      // Use the API key from initialization if not available from env
      const apiKey = REVENUECAT_REST_API_KEY || this.apiKey;

      console.log('[RevenueCat] Fetching products via REST API...');

      // Build the URL with query parameters
      let url = `https://api.revenuecat.com/v2/projects/${REVENUECAT_PROJECT_ID}/products`;

      // Add query parameters if provided
      const queryParams = [];
      if (options.limit) {
        queryParams.push(`limit=${options.limit}`);
      }
      if (options.startingAfter) {
        queryParams.push(`starting_after=${options.startingAfter}`);
      }

      if (queryParams.length > 0) {
        url += `?${queryParams.join('&')}`;
      }

      console.log(`[RevenueCat] API Request URL: ${url}`);

      // Make the API request
      const response = await fetch(url, {
        method: 'GET',
        headers: {
          'Authorization': `Bearer ${apiKey}`,
          'Content-Type': 'application/json',
          'Accept': 'application/json'
        }
      });

      // Check if the request was successful
      if (!response.ok) {
        const errorData = await response.json().catch(() => null);
        console.error('[RevenueCat] API Error:', response.status, errorData);
        throw new Error(`API request failed with status ${response.status}: ${JSON.stringify(errorData)}`);
      }

      // Parse the response
      const data = await response.json();
      console.log('[RevenueCat] Products fetched successfully:', data.items?.length || 0);

      // Format the response for easier consumption
      return {
        products: data.items || [],
        count: data.items?.length || 0,
        nextPage: data.next_page,
        url: data.url,
        raw: data
      };
    } catch (error) {
      console.error('[RevenueCat] Failed to get products:', error);

      throw error;
    }
  }

  /**
   * Get diagnostic information
   * @returns {Object} Diagnostic info
   */
  static getDiagnosticInfo() {
    return {
      isInitialized: this.isInitialized,
      apiKey: this.apiKey ? `${this.apiKey.substring(0, 8)}...` : 'Not set',
      projectId: REVENUECAT_PROJECT_ID || 'Not set',
      restApiEnabled: !!REVENUECAT_PROJECT_ID,
      sdkVersion: '7.x.x', // This would be dynamically retrieved in a real implementation
    };
  }

  /**
   * Check if RevenueCat is available
   * @returns {boolean} True if available
   */
  static isAvailable() {
    try {
      /* console.log('[RevenueCat] Checking availability...');
      console.log('[RevenueCat] Platform:', Platform.OS); */

      // Check if Purchases object exists
      if (!Purchases) {
        /* console.log('[RevenueCat] Purchases object is null/undefined'); */
        return false;
      }
      /* 
            console.log('[RevenueCat] Purchases object type:', typeof Purchases);
            console.log('[RevenueCat] Purchases methods:', Object.keys(Purchases || {}));
       */
      // Check if configure method exists
      if (typeof Purchases.configure !== 'function') {
        /* console.log('[RevenueCat] Purchases.configure is not a function'); */
        return false;
      }

      // Check native module availability (Android specific) - TEMPORARILY DISABLED FOR DEBUGGING
      if (Platform.OS === 'android') {
        /* console.log('[RevenueCat] Checking Android native module...');
        console.log('[RevenueCat] NativeModules.RCPurchases:', typeof NativeModules.RCPurchases);
        console.log('[RevenueCat] All available native modules:', Object.keys(NativeModules));
        console.log('[RevenueCat] Purchase-related modules:', Object.keys(NativeModules).filter(key => key.toLowerCase().includes('purchase') || key.toLowerCase().includes('rc')));
 */
        // TEMPORARILY DISABLED - Let's see what modules are actually available
        // if (!NativeModules.RCPurchases) {
        //   console.log('[RevenueCat] Android native module RCPurchases not found');
        //   console.log('[RevenueCat] Available native modules:', Object.keys(NativeModules).filter(key => key.includes('RC') || key.includes('Purchase')));
        //   return false;
        // }
      }

      /* console.log('[RevenueCat] SDK appears to be available'); */
      return true;
    } catch (error) {
      console.error('[RevenueCat] Error checking availability:', error);
      return false;
    }
  }

  /**
   * Get exam-specific plans from RevenueCat offerings
   * @param {Object} offerings - RevenueCat offerings object
   * @param {string} examCode - Exam code to filter plans for
   * @returns {Array} Array of exam-specific plan objects
   */
  static getExamSpecificPlans(offerings, examCode) {
    try {
      if (!offerings?.current?.availablePackages?.length) {
        console.log('[RevenueCat] No RevenueCat packages available');
        return [];
      }

      if (!examCode) {
        console.log('[RevenueCat] No exam code provided, using generic plans');
        return this.processRevenueCatOfferings(offerings);
      }

      const packages = offerings.current.availablePackages;
      console.log(`[RevenueCat] Looking for exam-specific plans for: ${examCode}`);

      // Filter packages that match the exam code pattern: [exam_code]:[plan_frequency]
      const examSpecificPackages = packages.filter(pkg => {
        const identifier = pkg.identifier?.toLowerCase() || '';
        const targetExamCode = examCode.toLowerCase().replace('-', '_');

        // Check if identifier starts with exam code followed by colon
        return identifier.startsWith(`${targetExamCode}:`);
      });

      console.log(`[RevenueCat] Found ${examSpecificPackages.length} exam-specific packages for ${examCode}`);

      // Log all available package identifiers for debugging
      console.log('[RevenueCat] All available package identifiers:',
        packages.map(pkg => pkg.identifier).join(', '));

      // Log the exam-specific packages found
      if (examSpecificPackages.length > 0) {
        console.log('[RevenueCat] Exam-specific package identifiers:',
          examSpecificPackages.map(pkg => pkg.identifier).join(', '));
      }

      if (examSpecificPackages.length === 0) {
        console.log(`[RevenueCat] No exam-specific packages found for ${examCode}`);
        console.log('[RevenueCat] Available exam codes in offerings:',
          [...new Set(packages
            .map(pkg => pkg.identifier?.split(':')[0])
            .filter(code => code && code.includes('_'))
          )].join(', '));

        // Return empty array - no plans available for this specific exam
        // This will show the "No Plans Available" message
        return [];
      }

      // Process exam-specific packages
      const examPlans = [];

      examSpecificPackages.forEach(pkg => {
        const identifier = pkg.identifier?.toLowerCase() || '';
        const parts = identifier.split(':');

        if (parts.length !== 2) {
          console.warn(`[RevenueCat] Invalid identifier format: ${identifier}`);
          return;
        }

        const [examCodePart, planFrequency] = parts;

        // Map plan frequency to our plan structure
        let planId, planName, period, aiCredit, isPopular;

        switch (planFrequency) {
          case 'weekly':
            planId = 'weekly';
            planName = 'Weekly Subscription';
            period = 'week';
            aiCredit = 50;
            isPopular = false;
            break;
          case 'monthly':
            planId = 'monthly';
            planName = '1-Month Subscription';
            period = 'month';
            aiCredit = 300;
            isPopular = true;
            break;
          case 'bimonthly':
            planId = 'bimonthly';
            planName = '2-Month Subscription';
            period = '2 months';
            aiCredit = 1000;
            isPopular = false;
            break;
          default:
            console.warn(`[RevenueCat] Unknown plan frequency: ${planFrequency}`);
            return;
        }

        console.log('[RevenueCat] Processing package:', pkg);
        examPlans.push({
          id: `${examCodePart}_${planFrequency}`, // Make ID unique by including exam code
          name: planName,
          price: pkg.product?.priceString || '$19.99',
          discountPrice: pkg.product?.priceString || '$14.99',
          aiCredit,
          period,
          isPopular,
          isAutoRenewing: true,
          productId: pkg.product?.identifier || `${examCodePart}.${planFrequency}`,
          examCode: examCodePart,
          planFrequency,
          planType: planId, // Keep the original plan type for logic
          currencyCode: pkg.product?.currencyCode || 'USD',
          // Store the original RevenueCat package for purchase
          revenueCatPackage: pkg
        });
      });

      // Sort plans in the desired order: weekly, monthly, bimonthly
      const planOrder = { 'weekly': 1, 'monthly': 2, 'bimonthly': 3 };
      examPlans.sort((a, b) => {
        const orderA = planOrder[a.planFrequency] || 999;
        const orderB = planOrder[b.planFrequency] || 999;
        return orderA - orderB;
      });

      console.log(`[RevenueCat] Processed ${examPlans.length} exam-specific plans:`, examPlans.map(p => `${p.planFrequency} (${p.name})`));
      return examPlans;

    } catch (error) {
      console.error('[RevenueCat] Error getting exam-specific plans:', error);
      return [];
    }
  }

  /**
   * Process RevenueCat offerings into plan format (fallback for generic plans)
   * @param {Object} offerings - RevenueCat offerings object
   * @returns {Array} Array of plan objects
   */
  static processRevenueCatOfferings(offerings) {
    try {
      if (!offerings?.current?.availablePackages?.length) {
        console.log('[RevenueCat] No RevenueCat packages available');
        return [];
      }

      const packages = offerings.current.availablePackages;
      console.log('[RevenueCat] Processing', packages.length, 'RevenueCat packages');

      const plans = packages.map((pkg) => {
        // Map package types to our plan IDs
        let planId = 'monthly'; // default
        let planName = 'Monthly Package';
        let period = 'month';
        let aiCredit = 300;
        let isPopular = false;

        // Map based on package identifier or type
        const identifier = pkg.identifier?.toLowerCase() || '';
        const packageType = pkg.packageType?.toLowerCase() || '';

        if (identifier.includes('weekly') || packageType.includes('weekly')) {
          planId = 'weekly';
          planName = 'Weekly Package';
          period = 'week';
          aiCredit = 50;
        } else if (identifier.includes('monthly') || packageType.includes('monthly')) {
          planId = 'monthly';
          planName = '1-Month Subscription';
          period = 'month';
          aiCredit = 300;
          isPopular = true;
        } else if (identifier.includes('bimonthly') || identifier.includes('2month') || packageType.includes('two_month')) {
          planId = 'bimonthly';
          planName = '2-Month Subscription';
          period = '2 months';
          aiCredit = 1000;
          isPopular = false;
        }

        return {
          id: planId,
          name: planName,
          price: pkg.product?.priceString || '$19.99',
          discountPrice: pkg.product?.priceString || '$14.99', // Use actual price as discount price
          aiCredit,
          period,
          isPopular,
          isAutoRenewing: true,
          productId: pkg.product?.identifier || `com.testapp.${planId}`,
          // Store the original RevenueCat package for purchase
          revenueCatPackage: pkg
        };
      });

      // Sort plans in the desired order: weekly, monthly, bimonthly
      const planOrder = { 'weekly': 1, 'monthly': 2, 'bimonthly': 3 };
      plans.sort((a, b) => {
        const orderA = planOrder[a.id] || 999;
        const orderB = planOrder[b.id] || 999;
        return orderA - orderB;
      });

      return plans;
    } catch (error) {
      console.error('[RevenueCat] Error processing RevenueCat offerings:', error);
      return [];
    }
  }
}

// Initialize static properties
RevenueCatService.isInitialized = false;
RevenueCatService.apiKey = null;

export default RevenueCatService;