import React, { useState } from 'react';
import { View, StyleSheet, Alert, ActivityIndicator, TouchableOpacity } from 'react-native';
import { Modal, Portal, Text, Button, useTheme, Card, IconButton } from 'react-native-paper';
import { useAICredit } from '../store/AICreditContext';
import { usePurchase } from '../store/PurchaseContext';
import LoginModal from './LoginModal';
import { useLogin } from '../store/LoginContext';
import { CREDIT_VIA_AD, CREDIT_VIA_PURCHASE } from '@env';
import AdmobService from '../services/AdmobService';
/**
 * A modal component for managing AI credits
 */
const AICreditModal = ({ visible, onDismiss }) => {
  const { colors } = useTheme();
  const [loading, setLoading] = useState(false);
  const [loginModalVisible, setLoginModalVisible] = useState(false);

  const { credits, addCredits } = useAICredit();
  const { isLoggedIn, user } = useLogin() || {};
  const { addPurchase, refreshPurchases } = usePurchase();

  // Handle successful login
  const handleLoginSuccess = (user) => {
    setLoginModalVisible(false);
  };

  // Get credit amounts from environment variables
  const creditViaAD = parseInt(CREDIT_VIA_AD || '10', 10);
  const CreditViaPurchase = parseInt(CREDIT_VIA_PURCHASE || '100', 10);

  // Handle watching ad for credits
  const handleWatchAd = async () => {
    try {
      setLoading(true);

      // Add timeout for ad loading (15 seconds)
      const timeoutPromise = new Promise((_, reject) => 
        setTimeout(() => reject(new Error('Ad loading timed out')), 15000)
      );

      // Create a promise to handle the rewarded ad flow
      const adResult = await Promise.race([
        new Promise((resolve, reject) => {
          // Load and show the rewarded ad
          AdmobService.showRewardedAd(
            (reward) => {
              // Reward earned callback
              resolve(reward);
            },
            () => {
              // Ad closed callback
              resolve(null);
            },
            (error) => {
              // Ad loading failed callback
              reject(error);
            }
          );
        }),
        timeoutPromise
      ]);

      if (adResult) {
        // Add credits from environment variable
        await addCredits(creditViaAD);

        Alert.alert(
          'Credits Added',
          `${creditViaAD} AI credits added to your account!`,
          [{ text: 'OK' }]
        );
      } else {
        Alert.alert(
          'Ad Closed',
          'You closed the ad before earning credits. Please watch the full ad to earn credits.',
          [{ text: 'OK' }]
        );
      }
    } catch (error) {
      console.error('Rewarded ad error:', error);
      Alert.alert(
        'Ad Error',
        error.message || 'Failed to load or show the ad. Please try again.',
        [{ text: 'OK' }]
      );
    } finally {
      setLoading(false);
    }
  };

  // Handle purchasing credits
  const handlePurchaseCredits = async () => {
    if (!isLoggedIn) {
      setLoginModalVisible(true);
      return;
    }

    try {
      setLoading(true);

      // Simulate purchase delay
      await new Promise(resolve => setTimeout(resolve, 1500));

      // Add credits from environment variable to AI Credit context
      await addCredits(CreditViaPurchase);

      // Create a special AI Credits purchase in PurchaseContext
      if (user && user.id) {
        // Generate a unique ID for this AI credit purchase
        const timestamp = new Date().getTime();
        const uniqueId = `ai_credits_${timestamp}`;

        console.log(`[AICreditModal] Creating AI credit purchase with unique ID: ${uniqueId}`);

        // Use the unique ID as additional info to ensure it's stored with the purchase
        // Note: AI credits never expire, so we don't need to pass daysValid
        const aiCreditPurchase = await addPurchase('ai_credits', null, {
          uniqueId: uniqueId
        }); // AI credits never expire

        // Add additional information to the purchase
        aiCreditPurchase.purchaseType = 'ai_credits';
        aiCreditPurchase.creditAmount = CreditViaPurchase;
        aiCreditPurchase.price = '$5 USD';
        aiCreditPurchase.purchaseDate = new Date().toISOString();
        aiCreditPurchase.uniqueId = uniqueId; // Ensure uniqueId is included

        // Refresh purchases to update the UI
        await refreshPurchases();
      }

      Alert.alert(
        'Purchase Successful',
        `${CreditViaPurchase} AI credits added to your account! You can view this purchase in My Purchases.`,
        [{ text: 'OK' }]
      );
    } catch (error) {
      console.error('AI Credit purchase error:', error);
      Alert.alert(
        'Purchase Failed',
        'Failed to purchase credits. Please try again.',
        [{ text: 'OK' }]
      );
    } finally {
      setLoading(false);
    }
  };

  return (
    <Portal>
      {/* Login Modal */}
      <LoginModal
        visible={loginModalVisible}
        onDismiss={() => setLoginModalVisible(false)}
        onLoginSuccess={handleLoginSuccess}
        source="aiCredit"
      />

      {/* AI Credit Modal */}
      <Modal
        visible={visible}
        onDismiss={onDismiss}
        contentContainerStyle={[
          styles.container,
          { backgroundColor: colors.surface }
        ]}
      >
        <IconButton
          icon="close"
          size={24}
          onPress={onDismiss}
          style={styles.closeButton}
        />

        {/* <Text style={[styles.title, { color: colors.text }]}>
          AI Credits
        </Text> */}

        <View style={styles.creditInfoContainer}>
          <Text style={[styles.creditCount, { color: colors.primary }]}>
            {credits}
          </Text>
          <Text style={[styles.creditLabel, { color: colors.onSurfaceVariant }]}>
            Available Credits
          </Text>
        </View>

        <Text style={[styles.description, { color: colors.onSurfaceVariant }]}>
          1 credit is used each time you receive a message from the AI assistant.
          {/* Get more credits by watching ads or making a purchase. */}
        </Text>

        {loading ? (
          <View style={styles.loadingContainer}>
            <ActivityIndicator size="large" color={colors.primary} />
            <Text style={{ color: colors.text, marginTop: 16 }}>
              Processing...
            </Text>
          </View>
        ) : (
          <View style={styles.optionsContainer}>
            <TouchableOpacity
              onPress={handleWatchAd}
              style={{
                borderWidth: 2,
                borderColor: colors.primary,
                borderRadius: 10,
                padding: 14,
                backgroundColor: `${colors.primary}20`,
                flexDirection: 'row',
                justifyContent: 'space-between',
                alignItems: 'center',
                marginBottom: 16
              }}
            >
              <View>
                <Text style={[styles.optionTitle, { color: colors.onSurface }]}>
                  Watch Ad
                </Text>
                <Text style={[styles.optionDescription, { color: colors.onSurfaceVariant }]}>
                  Get {CREDIT_VIA_AD} Credits
                </Text>
              </View>
              <IconButton
                icon="play-circle"
                size={24}
                iconColor={colors.primary}
                style={{ margin: 0 }}
              />
            </TouchableOpacity>

            {/* <TouchableOpacity
              onPress={handlePurchaseCredits}
              style={{
                borderWidth: 2,
                borderColor: colors.primary,
                borderRadius: 10,
                padding: 14,
                backgroundColor: `${colors.primary}20`,
                flexDirection: 'row',
                justifyContent: 'space-between',
                alignItems: 'center',
              }}
            >
              <View>
                <Text style={[styles.optionTitle, { color: colors.onSurface }]}>
                  Purchase
                </Text>
                <Text style={[styles.optionDescription, { color: colors.onSurfaceVariant }]}>
                  Get {CreditViaPurchase} Credits
                </Text>
              </View>
              <Text style={[styles.discountPrice, { color: colors.primary, fontWeight: 'bold' }]}>
                $5 USD
              </Text>
            </TouchableOpacity> */}
          </View>
        )}
      </Modal>
    </Portal>
  );
};

const styles = StyleSheet.create({
  container: {
    margin: 20,
    padding: 24,
    borderRadius: 16,
    elevation: 4,
  },
  closeButton: {
    position: 'absolute',
    right: 10,
    top: 10,
    zIndex: 1,
  },
  title: {
    fontSize: 24,
    fontWeight: 'bold',
    marginTop: 16,
    marginBottom: 24,
    textAlign: 'center',
  },
  creditInfoContainer: {
    alignItems: 'center',
    marginBottom: 24,
  },
  creditCount: {
    fontSize: 48,
    fontWeight: 'bold',
  },
  creditLabel: {
    fontSize: 16,
    marginTop: 8,
  },
  description: {
    fontSize: 16,
    marginBottom: 24,
    textAlign: 'center',
    lineHeight: 22,
  },
  optionsContainer: {
    gap: 16,
  },
  optionCard: {
    marginBottom: 16,
    borderRadius: 12,
    overflow: 'hidden',
  },
  optionContent: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    alignItems: 'center',
    paddingVertical: 8,
  },
  optionTitle: {
    fontSize: 18,
    fontWeight: 'bold',
    marginBottom: 4,
  },
  optionDescription: {
    fontSize: 14,
  },
  priceContainer: {
    flexDirection: 'row',
    alignItems: 'baseline',
  },
  discountPrice: {
    fontSize: 20,
    fontWeight: 'bold',
  },
  price: {
    fontSize: 18,
    fontWeight: 'bold',
  },
  optionButton: {
    width: '100%',
    paddingVertical: 8,
    marginTop: 8,
  },
  loadingContainer: {
    alignItems: 'center',
    justifyContent: 'center',
    padding: 24,
  },
});

export default AICreditModal;