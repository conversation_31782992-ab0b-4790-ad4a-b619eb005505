import React, { createContext, useContext, useState, useEffect } from 'react';
import AsyncStorage from '@react-native-async-storage/async-storage';
import { usePurchase } from './PurchaseContext';
import { useLogin } from './LoginContext';
import RealTimeSyncService from '../services/RealTimeSyncService';

// Create the context
const AICreditContext = createContext();

// Default credit amounts
const DEFAULT_FREE_CREDITS = 10;
const DEFAULT_PREMIUM_CREDITS = 200;

/**
 * Provider component for AI credit state
 */
export const AICreditProvider = ({ children }) => {
  const [credits, setCredits] = useState(0);
  const [userId, setUserId] = useState(null);
  const [loading, setLoading] = useState(false);
  const [error, setError] = useState(null);

  // Get purchase context to check if user has premium access
  const { isSubscriptionActive } = usePurchase();

  // Use LoginContext to get user information
  const { user, isLoggedIn } = useLogin() || {};

  // Load user ID and credits on mount and when login state changes
  useEffect(() => {
    const initialize = async () => {
      try {
        if (isLoggedIn && user && user.id) {
          /* console.log('User found in AICreditContext:', user.id); */
          setUserId(user.id);
          await loadCredits(user.id);
        } else {
          /* console.log('No user found in AICreditContext, using default credits'); */
          // Set default credits for non-logged in users
          setCredits(DEFAULT_FREE_CREDITS);
          setUserId(null);
        }
      } catch (error) {
        console.error('Error initializing AICreditContext:', error);
        // Don't show error to user, just set default credits
        setCredits(DEFAULT_FREE_CREDITS);
      }
    };

    initialize();
  }, [isLoggedIn, user]);

  /**
   * Load credits for a specific user
   * @param {string} uid - User ID
   */
  const loadCredits = async (uid) => {
    try {
      setLoading(true);
      setError(null);

      // Load credits from AsyncStorage
      const creditsJson = await AsyncStorage.getItem(`ai_credits_${uid}`);

      if (creditsJson) {
        const parsedCredits = JSON.parse(creditsJson);
        setCredits(parsedCredits.credits);
      } else {
        // If no credits found, set default credits
        const defaultCredits = DEFAULT_FREE_CREDITS;
        setCredits(defaultCredits);
        await saveCredits(defaultCredits, uid);
      }
    } catch (error) {
      console.error('Error loading credits:', error);
      setError('Failed to load credits');
      setCredits(DEFAULT_FREE_CREDITS);
    } finally {
      setLoading(false);
    }
  };

  /**
   * Save credits to AsyncStorage
   * @param {number} creditsToSave - Credits to save
   * @param {string} uid - User ID
   */
  const saveCredits = async (creditsToSave, uid) => {
    try {
      await AsyncStorage.setItem(`ai_credits_${uid}`, JSON.stringify({ credits: creditsToSave }));
    } catch (error) {
      console.error('Error saving credits:', error);
      setError('Failed to save credits');
    }
  };

  /**
   * Add credits to user's account
   * @param {number} amount - Amount of credits to add
   * @returns {number} - New credit balance
   */
  const addCredits = async (amount) => {
    try {
      setLoading(true);
      setError(null);

      const newCredits = credits + amount;
      setCredits(newCredits);

      // TODO: remote storage... If user is logged in, save to storage
      /* if (userId) {
        await saveCredits(newCredits, userId);

        // Sync AI credit balance to MongoDB in real-time using RealTimeSyncService
        try {
          console.log('[AICreditContext] Syncing AI credit balance to MongoDB...');
          const transaction = {
            type: 'purchase',
            amount: amount,
            creditAmount: amount,
            transactionId: `ai_credit_add_${Date.now()}`,
            purchaseDate: new Date().toISOString(),
            uniqueId: `ai_credit_${userId}_${Date.now()}`
          };

          const mongoResponse = await RealTimeSyncService.syncAICreditBalanceToMongoDB(userId, newCredits, transaction);
          console.log('[AICreditContext] AI credit balance synced to MongoDB successfully:', mongoResponse);
        } catch (syncError) {
          console.error('[AICreditContext] Failed to sync AI credit balance to MongoDB:', syncError);
          // Continue with local storage even if MongoDB sync fails
        }
      } */

      return newCredits;
    } catch (error) {
      console.error('Error adding credits:', error);
      setError(error.message || 'Failed to add credits');
      // Don't throw error, just return current credits
      return credits;
    } finally {
      setLoading(false);
    }
  };

  /**
   * Use credits (deduct from balance)
   * @param {number} amount - Amount of credits to use
   * @returns {boolean} - Whether the operation was successful
   */
  const useCredits = async (amount = 1) => {
    try {
      // Check if user is logged in
      if (!isLoggedIn || !userId) {
        console.warn('Cannot use credits: User not logged in');
        return false;
      }

      // Check if user has enough credits
      if (credits < amount) {
        console.warn('Cannot use credits: Not enough credits');
        return false;
      }

      setLoading(true);
      setError(null);

      const newCredits = credits - amount;
      setCredits(newCredits);
      await saveCredits(newCredits, userId);

      return true;
    } catch (error) {
      console.error('Error using credits:', error);
      setError('Failed to use credits');
      return false;
    } finally {
      setLoading(false);
    }
  };

  /**
   * Add premium credits when user purchases premium access
   * @param {string} examId - Exam ID that was purchased
   * @param {number} creditAmount - Amount of credits to add (defaults to DEFAULT_PREMIUM_CREDITS)
   */
  const addPremiumCredits = async (examId, creditAmount = DEFAULT_PREMIUM_CREDITS) => {
    try {
      if (!userId) {
        throw new Error('User not logged in');
      }

      setLoading(true);
      setError(null);

      // Add the specified amount of premium credits
      const newCredits = credits + creditAmount;
      setCredits(newCredits);
      await saveCredits(newCredits, userId);

      // Sync premium AI credit purchase to MongoDB in real-time using RealTimeSyncService
      try {
        console.log('[AICreditContext] Syncing premium AI credit purchase to MongoDB via RealTimeSyncService...');
        const creditData = {
          amount: creditAmount,
          creditAmount: creditAmount,
          examId: examId,
          transactionId: `premium_credits_${examId}_${Date.now()}`,
          purchaseDate: new Date().toISOString(),
          uniqueId: `premium_${examId}_${userId}_${Date.now()}`,
          paymentMethod: 'premium_purchase',
          price: 'Included with premium'
        };

        const mongoResponse = await RealTimeSyncService.syncAICreditPurchaseToMongoDB(creditData);
        console.log('[AICreditContext] Premium AI credit purchase synced to MongoDB successfully:', mongoResponse);
      } catch (syncError) {
        console.error('[AICreditContext] Failed to sync premium AI credit purchase to MongoDB:', syncError);
        // Continue with local storage even if MongoDB sync fails
      }

      console.log(`Added ${creditAmount} premium credits for exam ${examId}. New total: ${newCredits}`);
      return newCredits;
    } catch (error) {
      console.error('Error adding premium credits:', error);
      setError(error.message || 'Failed to add premium credits');
      return credits; // Return current credits on error
    } finally {
      setLoading(false);
    }
  };

  /**
   * Clear credits when user logs out
   */
  const clearCredits = () => {
    setCredits(0);
  };

  /**
   * Update user ID and load credits when user changes
   * @param {string} newUserId - New user ID
   */
  const updateUser = async (newUserId) => {
    if (newUserId === userId) return;

    setUserId(newUserId);

    if (newUserId) {
      await loadCredits(newUserId);
    } else {
      clearCredits();
    }
  };

  /**
   * Update credits from API data (called during login)
   * @param {Object} aiCreditsData - AI credits data from server
   */
  const updateCredits = async (aiCreditsData) => {
    try {
      console.log('[AICreditContext] ===== UPDATING CREDITS FROM API =====');
      console.log('[AICreditContext] Received AI credits data:', {
        totalCredits: aiCreditsData?.totalCredits || 0,
        usedCredits: aiCreditsData?.usedCredits || 0,
        availableCredits: aiCreditsData?.availableCredits || 0,
        transactionsCount: aiCreditsData?.transactions?.length || 0,
        creditsId: aiCreditsData?.creditsId
      });

      if (aiCreditsData) {
        // Update credits state with server data
        const availableCredits = aiCreditsData.availableCredits ||
                                ((aiCreditsData.totalCredits || 0) - (aiCreditsData.usedCredits || 0));

        setCredits(availableCredits);

        console.log('[AICreditContext] Credits updated from API:', {
          previousCredits: credits,
          newCredits: availableCredits,
          totalCredits: aiCreditsData.totalCredits,
          usedCredits: aiCreditsData.usedCredits
        });

        // Store updated credits in AsyncStorage
        const creditsKey = `ai_credits_${userId}`;
        const creditsData = {
          credits: availableCredits,
          totalCredits: aiCreditsData.totalCredits || 0,
          usedCredits: aiCreditsData.usedCredits || 0,
          transactions: aiCreditsData.transactions || [],
          lastUpdated: new Date().toISOString(),
          source: 'api_sync'
        };

        await AsyncStorage.setItem(creditsKey, JSON.stringify(creditsData));
        console.log('[AICreditContext] Credits data stored in AsyncStorage');
      } else {
        console.warn('[AICreditContext] No AI credits data provided');
      }

      console.log('[AICreditContext] ===== CREDITS UPDATE COMPLETE =====');
    } catch (error) {
      console.error('[AICreditContext] Error updating credits from API:', error);
    }
  };

  // Context value
  const value = {
    credits,
    loading,
    error,
    addCredits,
    useCredits,
    clearCredits,
    addPremiumCredits,
    updateUser,
    updateCredits
  };

  return (
    <AICreditContext.Provider value={value}>
      {children}
    </AICreditContext.Provider>
  );
};

/**
 * Hook to use the AI credit context
 */
export const useAICredit = () => {
  const context = useContext(AICreditContext);
  if (context === undefined) {
    throw new Error('useAICredit must be used within an AICreditProvider');
  }
  return context;
};

export default AICreditContext;
